# Import required libraries
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *

# ADLS file path configuration
filePath = r"abfss://<EMAIL>/"
bronze_path = filePath + "bronze/"

# Catalog and schema configuration
catalog = "adventure_works"
schema = "bronze_schema"

print(f"Base path: {filePath}")
print(f"Bronze path: {bronze_path}")
print(f"Target catalog: {catalog}.{schema}")

# List of bronze files with correct case-sensitive paths
bronze_files = [
    "Product/Product.parquet",
    "Region/Region.parquet",
    "Reseller/Reseller.parquet",
    "Sales/Sales.parquet",
    "Salesperson/Salesperson.parquet",
    "SalespersonRegion/SalespersonRegion.parquet",
    "Targets/Targets.parquet"
]

# Extract table names for processing (lowercase for consistency)
bronze_tables = [file_path.split('/')[0].lower() for file_path in bronze_files]

print(f"Bronze files: {bronze_files}")
print(f"Table names: {bronze_tables}")

def read_bronze_table(file_path, table_name):
    """Read a single bronze table from ADLS using the correct file path"""
    try:
        full_path = bronze_path + file_path
        print(f"Reading from: {full_path}")
        
        df = (spark.read
              .format('parquet')
              .load(full_path)
        )
        return df
    except Exception as e:
        print(f"Error reading {table_name} from {file_path}: {str(e)}")
        return None

def profile_dataframe(df, table_name):
    """Generate basic profiling information for a DataFrame"""
    print(f"\n📊 PROFILING: {table_name.upper()}")
    print("=" * 40)
    
    # Basic stats
    row_count = df.count()
    col_count = len(df.columns)
    
    print(f"Rows: {row_count:,}")
    print(f"Columns: {col_count}")
    print(f"Schema:")
    df.printSchema()
    
    # Column statistics
    print(f"\nColumn Details:")
    for col_name in df.columns:
        col_type = dict(df.dtypes)[col_name]
        null_count = df.filter(col(col_name).isNull()).count()
        null_pct = (null_count / row_count * 100) if row_count > 0 else 0
        
        print(f"  {col_name} ({col_type}): {null_count:,} nulls ({null_pct:.1f}%)")
    
    return {
        'table_name': table_name,
        'row_count': row_count,
        'column_count': col_count,
        'columns': df.columns
    }

def write_to_catalog(df, table_name, catalog, schema, mode="overwrite"):
    """Write DataFrame to the catalog as a Delta table"""
    try:
        full_table_name = f"{catalog}.{schema}.{table_name}"
        
        (df.write
         .format("delta")
         .mode(mode)
         .option("mergeSchema", "true")
         .saveAsTable(full_table_name)
        )
        
        print(f"✓ Successfully wrote {table_name} to {full_table_name}")
        return True
        
    except Exception as e:
        print(f"✗ Error writing {table_name} to catalog: {str(e)}")
        return False

# Main execution
print("🚀 Adventure Works Bronze Layer Processing")
print("=" * 50)

# Dictionary to store results
results = {}
dataframes = {}

# Process each table with correct file paths
for i, table_name in enumerate(bronze_tables):
    file_path = bronze_files[i]
    print(f"\n📂 Processing {table_name} from {file_path}...")
    
    # Read the table
    df = read_bronze_table(file_path, table_name)
    
    if df is not None:
        # Store DataFrame
        dataframes[f"df_{table_name}"] = df
        
        # Profile the data
        profile_info = profile_dataframe(df, table_name)
        results[table_name] = profile_info
        
        # Show sample data
        print(f"\n📋 Sample data from {table_name}:")
        df.show(3, truncate=False)
        
        # Create temporary view
        df.createOrReplaceTempView(f"bronze_{table_name}")
        print(f"✓ Created temporary view: bronze_{table_name}")

# Summary report
print("\n📈 SUMMARY REPORT")
print("=" * 50)
rows_list = [info['row_count'] for info in results.values()]
total_rows = sum(rows_list)
print(f"Total tables processed: {len(results)}")
print(f"Total rows across all tables: {total_rows:,}")

print(f"\nTable Summary:")
for table_name, info in results.items():
    print(f"  {table_name}: {info['row_count']:,} rows, {info['column_count']} columns")

print(f"\nAvailable DataFrames: {list(dataframes.keys())}")
print(f"Available Views: {['bronze_' + table for table in bronze_tables]}")

# Optional: Write to catalog (uncomment to execute)
# print(f"\n💾 Writing to catalog {catalog}.{schema}...")
# for table_name in bronze_tables:
#     if f"df_{table_name}" in dataframes:
#         write_to_catalog(dataframes[f"df_{table_name}"], table_name, catalog, schema)

# Example: Product analysis
if 'df_product' in dataframes:
    print("Product DataFrame analysis:")
    dataframes['df_product'].describe().show()

# Example: Sales summary via SQL
try:
    print("Sales summary:")
    spark.sql("""
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT SalesOrderNumber) as unique_orders
        FROM bronze_sales
    """).show()
except:
    print("Sales table not available for analysis")