{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Adventure Works Silver Layer - Advanced Analytics\n", "\n", "This notebook creates advanced analytical tables and metrics for the Silver layer.\n", "\n", "## Advanced Transformations:\n", "1. **Customer Lifetime Value (CLV)** calculations\n", "2. **Product Performance Metrics** with rankings\n", "3. **Sales Trend Analysis** with moving averages\n", "4. **Territory Performance** comparisons\n", "5. **Cohort Analysis** for customer behavior\n", "6. **ABC Analysis** for inventory management"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql.functions import *\n", "from pyspark.sql.types import *\n", "from pyspark.sql.window import Window"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration\n", "catalog = \"adventworks\"\n", "bronze_schema = \"bronze_schema\"\n", "silver_schema = \"silver_schema\"\n", "\n", "spark.sql(f\"USE CATALOG {catalog}\")\n", "spark.sql(f\"USE SCHEMA {bronze_schema}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Customer Analytics & Lifetime Value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read sales data for customer analysis\n", "df_sales = spark.table(f\"{catalog}.{bronze_schema}.sales\")\n", "\n", "# Customer Lifetime Value and Behavior Analysis\n", "customer_window = Window.partitionBy(\"CustomerKey\")\n", "customer_order_window = Window.partitionBy(\"CustomerKey\").orderBy(\"OrderDate\")\n", "\n", "df_customer_analytics = df_sales.select(\n", "    col(\"CustomerKey\"),\n", "    col(\"OrderDate\"),\n", "    col(\"SalesAmount\"),\n", "    col(\"OrderQuantity\"),\n", "    col(\"ProductKey\")\n", ").groupBy(\"CustomerKey\").agg(\n", "    # 💰 FINANCIAL METRICS\n", "    sum(\"SalesAmount\").alias(\"TotalLifetimeValue\"),\n", "    avg(\"SalesAmount\").alias(\"AverageOrderValue\"),\n", "    max(\"SalesAmount\").alias(\"LargestOrderValue\"),\n", "    min(\"SalesAmount\").alias(\"SmallestOrderValue\"),\n", "    \n", "    # 📊 BEHAVIORAL METRICS\n", "    count(\"*\").alias(\"TotalOrders\"),\n", "    sum(\"OrderQuantity\").alias(\"TotalItemsPurchased\"),\n", "    avg(\"OrderQuantity\").alias(\"AverageItemsPerOrder\"),\n", "    countDistinct(\"ProductKey\").alias(\"UniqueProductsPurchased\"),\n", "    \n", "    # 📅 TEMPORAL METRICS\n", "    min(\"OrderDate\").alias(\"FirstOrderDate\"),\n", "    max(\"OrderDate\").alias(\"LastOrderDate\"),\n", "    datediff(max(\"OrderDate\"), min(\"OrderDate\")).alias(\"CustomerLifespanDays\")\n", ").withColumn(\n", "    # 🎯 CUSTOMER SEGMENTATION\n", "    \"CustomerValueSegment\",\n", "    when(col(\"TotalLifetimeValue\") >= 10000, \"VIP\")\n", "    .when(col(\"TotalLifetimeValue\") >= 5000, \"High-Value\")\n", "    .when(col(\"TotalLifetimeValue\") >= 1000, \"Medium-Value\")\n", "    .otherwise(\"Low-Value\")\n", ").withColumn(\n", "    \"CustomerFrequencySegment\",\n", "    when(col(\"TotalOrders\") >= 10, \"Frequent\")\n", "    .when(col(\"TotalOrders\") >= 5, \"Regular\")\n", "    .when(col(\"TotalOrders\") >= 2, \"Occasional\")\n", "    .otherwise(\"One-Time\")\n", ").withColumn(\n", "    \"CustomerRecencySegment\",\n", "    when(datediff(current_date(), col(\"LastOrderDate\")) <= 30, \"Recent\")\n", "    .when(datediff(current_date(), col(\"LastOrderDate\")) <= 90, \"Active\")\n", "    .when(datediff(current_date(), col(\"LastOrderDate\")) <= 365, \"Dormant\")\n", "    .otherwise(\"Inactive\")\n", ").withColumn(\n", "    # Calculate order frequency (orders per year)\n", "    \"OrderFrequencyPerYear\",\n", "    when(col(\"CustomerLifespanDays\") > 0, \n", "         col(\"TotalOrders\") * 365.0 / col(\"CustomerLifespanDays\"))\n", "    .otherwise(col(\"TotalOrders\"))\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ Customer Analytics completed\")\n", "df_customer_analytics.show(5, truncate=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Product Performance Analytics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Product Performance with Rankings\n", "df_product = spark.table(f\"{catalog}.{bronze_schema}.product\")\n", "\n", "# Join sales with product data for comprehensive analysis\n", "df_product_performance = df_sales.join(df_product, \"ProductKey\").groupBy(\n", "    \"ProductKey\", \"Product\", \"Category\", \"Subcategory\", \"Standard_Cost\"\n", ").agg(\n", "    # 📈 SALES PERFORMANCE\n", "    sum(\"SalesAmount\").alias(\"TotalRevenue\"),\n", "    sum(\"OrderQuantity\").alias(\"TotalUnitsSold\"),\n", "    count(\"*\").alias(\"TotalOrders\"),\n", "    countDistinct(\"CustomerKey\").alias(\"UniqueCustomers\"),\n", "    avg(\"SalesAmount\").alias(\"AverageOrderValue\"),\n", "    avg(\"OrderQuantity\").alias(\"AverageQuantityPerOrder\")\n", ").withColumn(\n", "    # 💰 PROFITABILITY METRICS\n", "    \"EstimatedProfit\", \n", "    col(\"TotalRevenue\") - (col(\"TotalUnitsSold\") * col(\"Standard_Cost\"))\n", ").withColumn(\n", "    \"ProfitMarginPercent\",\n", "    when(col(\"TotalRevenue\") > 0, \n", "         (col(\"EstimatedProfit\") / col(\"TotalRevenue\") * 100))\n", "    .otherwise(0)\n", ").withColumn(\n", "    \"RevenuePerCustomer\",\n", "    when(col(\"UniqueCustomers\") > 0, col(\"TotalRevenue\") / col(\"UniqueCustomers\"))\n", "    .otherwise(0)\n", ")\n", "\n", "# Add rankings\n", "revenue_window = Window.orderBy(desc(\"TotalRevenue\"))\n", "units_window = Window.orderBy(desc(\"TotalUnitsSold\"))\n", "profit_window = Window.orderBy(desc(\"EstimatedProfit\"))\n", "\n", "df_product_performance = df_product_performance.withColumn(\n", "    \"RevenueRank\", row_number().over(revenue_window)\n", ").withColumn(\n", "    \"UnitsRank\", row_number().over(units_window)\n", ").withColumn(\n", "    \"ProfitRank\", row_number().over(profit_window)\n", ").withColumn(\n", "    # 🏆 PERFORMANCE CATEGORIES\n", "    \"RevenuePerformance\",\n", "    when(col(\"RevenueRank\") <= 10, \"Top 10\")\n", "    .when(col(\"RevenueRank\") <= 50, \"Top 50\")\n", "    .when(col(\"RevenueRank\") <= 100, \"Top 100\")\n", "    .otherwise(\"Others\")\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ Product Performance Analytics completed\")\n", "df_product_performance.orderBy(desc(\"TotalRevenue\")).show(5, truncate=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Sales Trend Analysis with Moving Averages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Daily Sales Trends with Moving Averages\n", "df_daily_sales = df_sales.groupBy(\"OrderDate\").agg(\n", "    sum(\"SalesAmount\").alias(\"DailySales\"),\n", "    sum(\"OrderQuantity\").alias(\"DailyUnits\"),\n", "    count(\"*\").alias(\"DailyOrders\"),\n", "    countDistinct(\"CustomerKey\").alias(\"DailyUniqueCustomers\")\n", ").orderBy(\"OrderDate\")\n", "\n", "# Define windows for moving averages\n", "days_7_window = Window.orderBy(\"OrderDate\").rowsBetween(-6, 0)\n", "days_30_window = Window.orderBy(\"OrderDate\").rowsBetween(-29, 0)\n", "days_90_window = Window.orderBy(\"OrderDate\").rowsBetween(-89, 0)\n", "\n", "df_sales_trends = df_daily_sales.withColumn(\n", "    # 📊 MOVING AVERAGES\n", "    \"Sales_7DayMA\", avg(\"DailySales\").over(days_7_window)\n", ").withColumn(\n", "    \"Sales_30DayMA\", avg(\"DailySales\").over(days_30_window)\n", ").withColumn(\n", "    \"Sales_90DayMA\", avg(\"DailySales\").over(days_90_window)\n", ").withColumn(\n", "    \"Orders_7DayMA\", avg(\"DailyOrders\").over(days_7_window)\n", ").withColumn(\n", "    \"Orders_30DayMA\", avg(\"DailyOrders\").over(days_30_window)\n", ").withColumn(\n", "    # 📈 GROWTH CALCULATIONS\n", "    \"SalesGrowth_7Day\",\n", "    when(lag(\"Sales_7DayMA\", 7).over(Window.orderBy(\"OrderDate\")) > 0,\n", "         ((col(\"Sales_7DayMA\") - lag(\"Sales_7DayMA\", 7).over(Window.orderBy(\"OrderDate\"))) / \n", "          lag(\"Sales_7DayMA\", 7).over(Window.orderBy(\"OrderDate\")) * 100))\n", "    .otherwise(0)\n", ").withColumn(\n", "    \"SalesGrowth_30Day\",\n", "    when(lag(\"Sales_30DayMA\", 30).over(Window.orderBy(\"OrderDate\")) > 0,\n", "         ((col(\"Sales_30DayMA\") - lag(\"Sales_30DayMA\", 30).over(Window.orderBy(\"OrderDate\"))) / \n", "          lag(\"Sales_30DayMA\", 30).over(Window.orderBy(\"OrderDate\")) * 100))\n", "    .otherwise(0)\n", ").withColumn(\n", "    # 🎯 TREND INDICATORS\n", "    \"TrendDirection\",\n", "    when(col(\"SalesGrowth_7Day\") > 5, \"Strong Growth\")\n", "    .when(col(\"SalesGrowth_7Day\") > 0, \"Growth\")\n", "    .when(col(\"SalesGrowth_7Day\") > -5, \"Stable\")\n", "    .otherwise(\"Decline\")\n", ").withColumn(\n", "    # Add date components for analysis\n", "    \"Year\", year(\"OrderDate\")\n", ").withColumn(\n", "    \"Month\", month(\"OrderDate\")\n", ").withColumn(\n", "    \"Quarter\", quarter(\"OrderDate\")\n", ").withColumn(\n", "    \"DayOfWeek\", dayofweek(\"OrderDate\")\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ Sales Trend Analysis completed\")\n", "df_sales_trends.orderBy(desc(\"OrderDate\")).show(5, truncate=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Territory Performance Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Territory Performance Analysis\n", "df_region = spark.table(f\"{catalog}.{bronze_schema}.region\")\n", "df_salesperson = spark.table(f\"{catalog}.{bronze_schema}.salesperson\")\n", "df_salesperson_region = spark.table(f\"{catalog}.{bronze_schema}.salespersonregion\")\n", "df_targets = spark.table(f\"{catalog}.{bronze_schema}.targets\")\n", "\n", "# Join sales with territory information\n", "df_territory_sales = df_sales.join(\n", "    df_salesperson_region, \"SalespersonKey\"\n", ").join(\n", "    df_region, \"SalesTerritoryKey\"\n", ").join(\n", "    df_targets, \"SalespersonKey\", \"left\"\n", ")\n", "\n", "# Territory performance metrics\n", "df_territory_performance = df_territory_sales.groupBy(\n", "    \"SalesTerritoryKey\", \"Region\", \"Country\", \"Group\"\n", ").agg(\n", "    # 💰 FINANCIAL PERFORMANCE\n", "    sum(\"SalesAmount\").alias(\"TotalRevenue\"),\n", "    avg(\"SalesAmount\").alias(\"AverageOrderValue\"),\n", "    sum(\"OrderQuantity\").alias(\"TotalUnits\"),\n", "    count(\"*\").alias(\"TotalOrders\"),\n", "    countDistinct(\"CustomerKey\").alias(\"UniqueCustomers\"),\n", "    countDistinct(\"Salesperson<PERSON><PERSON>\").alias(\"ActiveSalespeople\"),\n", "    sum(\"TargetBase\").alias(\"TotalTargets\")\n", ").withColumn(\n", "    # 📊 PERFORMANCE RATIOS\n", "    \"RevenuePerSalesperson\",\n", "    when(col(\"ActiveSalespeople\") > 0, col(\"TotalRevenue\") / col(\"ActiveSalespeople\"))\n", "    .otherwise(0)\n", ").withColumn(\n", "    \"RevenuePerCustomer\",\n", "    when(col(\"UniqueCustomers\") > 0, col(\"TotalRevenue\") / col(\"UniqueCustomers\"))\n", "    .otherwise(0)\n", ").withColumn(\n", "    \"TargetAchievementPercent\",\n", "    when(col(\"TotalTargets\") > 0, (col(\"TotalRevenue\") / col(\"TotalTargets\") * 100))\n", "    .otherwise(0)\n", ").withColumn(\n", "    # 🏆 PERFORMANCE RANKINGS\n", "    \"RevenueRank\",\n", "    row_number().over(Window.orderBy(desc(\"TotalRevenue\")))\n", ").withColumn(\n", "    \"TargetAchievementRank\",\n", "    row_number().over(Window.orderBy(desc(\"TargetAchievementPercent\")))\n", ").withColumn(\n", "    # 🎯 PERFORMANCE CATEGORIES\n", "    \"PerformanceCategory\",\n", "    when(col(\"TargetAchievementPercent\") >= 120, \"Exceeds Expectations\")\n", "    .when(col(\"TargetAchievementPercent\") >= 100, \"Meets Expectations\")\n", "    .when(col(\"TargetAchievementPercent\") >= 80, \"Below Expectations\")\n", "    .otherwise(\"Needs Improvement\")\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ Territory Performance Analysis completed\")\n", "df_territory_performance.orderBy(desc(\"TotalRevenue\")).show(5, truncate=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}