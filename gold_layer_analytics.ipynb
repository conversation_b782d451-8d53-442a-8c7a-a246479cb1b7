from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql.window import Window
from pyspark.ml.feature import VectorAssembler, StandardScaler
from pyspark.ml.stat import Correlation
import numpy as np

# Configuration
catalog = "adventworks"
bronze_schema = "bronze_schema"
silver_schema = "silver_schema"
gold_schema = "gold_schema"

spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"CREATE SCHEMA IF NOT EXISTS {catalog}.{gold_schema}")

print(f"✅ Gold schema {catalog}.{gold_schema} ready")

# Read from Silver layer (cleaned data)
df_sales = spark.table(f"{catalog}.{bronze_schema}.sales")
df_product = spark.table(f"{catalog}.{bronze_schema}.product")
df_targets = spark.table(f"{catalog}.{bronze_schema}.targets")
df_salesperson = spark.table(f"{catalog}.{bronze_schema}.salesperson")
df_region = spark.table(f"{catalog}.{bronze_schema}.region")

# Executive KPI Dashboard - Monthly aggregated metrics
df_executive_kpis = df_sales.join(df_product, "ProductKey") \
    .join(df_region, "SalesTerritoryKey") \
    .select(
        year(col("OrderDate")).alias("Year"),
        month(col("OrderDate")).alias("Month"),
        col("Region"),
        col("Country"),
        col("Category"),
        col("Sales").alias("Revenue"),
        col("Quantity"),
        col("Standard_Cost"),
        col("ResellerKey")
    ).groupBy("Year", "Month", "Region", "Country", "Category").agg(
        # 💰 FINANCIAL KPIs
        sum("Revenue").alias("TotalRevenue"),
        sum("Quantity").alias("TotalUnitsSold"),
        sum(col("Quantity") * col("Standard_Cost")).alias("TotalCOGS"),
        count("*").alias("TotalOrders"),
        countDistinct("ResellerKey").alias("ActiveCustomers"),
        
        # 📊 BUSINESS METRICS
        avg("Revenue").alias("AvgOrderValue"),
        avg("Quantity").alias("AvgOrderSize")
    ).withColumn(
        # 💡 CALCULATED KPIs
        "GrossProfit", col("TotalRevenue") - col("TotalCOGS")
    ).withColumn(
        "GrossProfitMargin", 
        when(col("TotalRevenue") > 0, col("GrossProfit") / col("TotalRevenue") * 100)
        .otherwise(0)
    ).withColumn(
        "RevenuePerCustomer",
        when(col("ActiveCustomers") > 0, col("TotalRevenue") / col("ActiveCustomers"))
        .otherwise(0)
    ).withColumn(
        "OrdersPerCustomer",
        when(col("ActiveCustomers") > 0, col("TotalOrders") / col("ActiveCustomers"))
        .otherwise(0)
    ).withColumn(
        # 📅 TIME DIMENSIONS
        "YearMonth", concat(col("Year"), lit("-"), lpad(col("Month"), 2, "0"))
    ).withColumn(
        "Quarter", 
        when(col("Month").isin([1,2,3]), "Q1")
        .when(col("Month").isin([4,5,6]), "Q2")
        .when(col("Month").isin([7,8,9]), "Q3")
        .otherwise("Q4")
    ).withColumn(
        "ProcessedTimestamp", current_timestamp()
    )

print("✅ Executive KPI Dashboard created")
df_executive_kpis.orderBy(desc("Year"), desc("Month"), desc("TotalRevenue")).show(5)

# Customer RFM Analysis (Recency, Frequency, Monetary) - Gold standard for marketing
analysis_date = spark.sql("SELECT max(OrderDate) as max_date FROM adventworks.bronze_schema.sales").collect()[0]['max_date']

df_customer_rfm = df_sales.groupBy("ResellerKey").agg(
    # 📅 RECENCY (days since last purchase)
    datediff(lit(analysis_date), max("OrderDate")).alias("Recency"),
    
    # 🔄 FREQUENCY (number of purchases)
    count("*").alias("Frequency"),
    
    # 💰 MONETARY (total spent)
    sum("Sales").alias("Monetary"),
    
    # 📊 ADDITIONAL METRICS
    avg("Sales").alias("AvgOrderValue"),
    countDistinct("ProductKey").alias("ProductDiversity"),
    min("OrderDate").alias("FirstPurchaseDate"),
    max("OrderDate").alias("LastPurchaseDate")
).withColumn(
    "CustomerLifespanDays",
    datediff(col("LastPurchaseDate"), col("FirstPurchaseDate"))
)

# Calculate RFM Scores (1-5 scale)
recency_quantiles = df_customer_rfm.approxQuantile("Recency", [0.2, 0.4, 0.6, 0.8], 0.01)
frequency_quantiles = df_customer_rfm.approxQuantile("Frequency", [0.2, 0.4, 0.6, 0.8], 0.01)
monetary_quantiles = df_customer_rfm.approxQuantile("Monetary", [0.2, 0.4, 0.6, 0.8], 0.01)

df_customer_segments = df_customer_rfm.withColumn(
    "R_Score",
    when(col("Recency") <= recency_quantiles[0], 5)
    .when(col("Recency") <= recency_quantiles[1], 4)
    .when(col("Recency") <= recency_quantiles[2], 3)
    .when(col("Recency") <= recency_quantiles[3], 2)
    .otherwise(1)
).withColumn(
    "F_Score",
    when(col("Frequency") >= frequency_quantiles[3], 5)
    .when(col("Frequency") >= frequency_quantiles[2], 4)
    .when(col("Frequency") >= frequency_quantiles[1], 3)
    .when(col("Frequency") >= frequency_quantiles[0], 2)
    .otherwise(1)
).withColumn(
    "M_Score",
    when(col("Monetary") >= monetary_quantiles[3], 5)
    .when(col("Monetary") >= monetary_quantiles[2], 4)
    .when(col("Monetary") >= monetary_quantiles[1], 3)
    .when(col("Monetary") >= monetary_quantiles[0], 2)
    .otherwise(1)
).withColumn(
    "RFM_Score", concat(col("R_Score"), col("F_Score"), col("M_Score"))
).withColumn(
    # 🎯 BUSINESS SEGMENTS
    "CustomerSegment",
    when((col("R_Score") >= 4) & (col("F_Score") >= 4) & (col("M_Score") >= 4), "Champions")
    .when((col("R_Score") >= 3) & (col("F_Score") >= 3) & (col("M_Score") >= 4), "Loyal Customers")
    .when((col("R_Score") >= 4) & (col("F_Score") <= 2), "New Customers")
    .when((col("R_Score") >= 3) & (col("F_Score") >= 3) & (col("M_Score") <= 3), "Potential Loyalists")
    .when((col("R_Score") <= 2) & (col("F_Score") >= 3) & (col("M_Score") >= 3), "At Risk")
    .when((col("R_Score") <= 2) & (col("F_Score") <= 2) & (col("M_Score") >= 4), "Can't Lose Them")
    .when((col("R_Score") <= 2) & (col("F_Score") <= 2) & (col("M_Score") <= 2), "Lost Customers")
    .otherwise("Others")
).withColumn(
    "ProcessedTimestamp", current_timestamp()
)

print("✅ Customer RFM Analysis completed")
print("\nCustomer Segments Distribution:")
df_customer_segments.groupBy("CustomerSegment").agg(
    count("*").alias("CustomerCount"),
    avg("Monetary").alias("AvgMonetaryValue")
).orderBy(desc("CustomerCount")).show()

# Product ABC Analysis for Inventory Management
df_product_performance = df_sales.join(df_product, "ProductKey").groupBy(
    "ProductKey", "Product", "Category", "Subcategory", "Standard_Cost"
).agg(
    sum("Sales").alias("TotalRevenue"),
    sum("Quantity").alias("TotalQuantitySold"),
    count("*").alias("TotalOrders"),
    countDistinct("ResellerKey").alias("UniqueCustomers"),
    avg("Sales").alias("AvgOrderValue"),
    stddev("Sales").alias("RevenueStdDev")
).withColumn(
    "TotalCost", col("TotalQuantitySold") * col("Standard_Cost")
).withColumn(
    "GrossProfit", col("TotalRevenue") - col("TotalCost")
).withColumn(
    "ProfitMargin",
    when(col("TotalRevenue") > 0, col("GrossProfit") / col("TotalRevenue") * 100)
    .otherwise(0)
)

# Calculate cumulative revenue percentage for ABC classification
total_revenue = df_product_performance.agg(sum("TotalRevenue")).collect()[0][0]

window_spec = Window.orderBy(desc("TotalRevenue"))
df_abc_analysis = df_product_performance.withColumn(
    "RevenueRank", row_number().over(window_spec)
).withColumn(
    "CumulativeRevenue", sum("TotalRevenue").over(window_spec.rowsBetween(Window.unboundedPreceding, 0))
).withColumn(
    "CumulativeRevenuePercent", col("CumulativeRevenue") / total_revenue * 100
).withColumn(
    # 📊 ABC CLASSIFICATION
    "ABC_Category",
    when(col("CumulativeRevenuePercent") <= 80, "A - High Value")
    .when(col("CumulativeRevenuePercent") <= 95, "B - Medium Value")
    .otherwise("C - Low Value")
).withColumn(
    # 🎯 BUSINESS RECOMMENDATIONS
    "InventoryStrategy",
    when(col("ABC_Category") == "A - High Value", "High Stock - Frequent Review")
    .when(col("ABC_Category") == "B - Medium Value", "Moderate Stock - Regular Review")
    .otherwise("Low Stock - Periodic Review")
).withColumn(
    "MarketingPriority",
    when((col("ABC_Category") == "A - High Value") & (col("ProfitMargin") >= 30), "High Priority")
    .when(col("ABC_Category") == "A - High Value", "Medium Priority")
    .otherwise("Low Priority")
).withColumn(
    "ProcessedTimestamp", current_timestamp()
)

print("✅ Product ABC Analysis completed")
print("\nABC Distribution:")
df_abc_analysis.groupBy("ABC_Category").agg(
    count("*").alias("ProductCount"),
    sum("TotalRevenue").alias("CategoryRevenue"),
    avg("ProfitMargin").alias("AvgProfitMargin")
).show()

# Time Series Dataset for Data Scientists and ML Engineers
df_time_series = df_sales.join(df_product, "ProductKey") \
    .join(df_region, "SalesTerritoryKey") \
    .select(
        col("OrderDate"),
        col("Sales").alias("Revenue"),
        col("Quantity"),
        col("Category"),
        col("Region"),
        col("Country")
    ).groupBy("OrderDate", "Category", "Region", "Country").agg(
        sum("Revenue").alias("DailyRevenue"),
        sum("Quantity").alias("DailyQuantity"),
        count("*").alias("DailyOrders")
    )

# Add time-based features for ML
df_ml_features = df_time_series.withColumn(
    "Year", year(col("OrderDate"))
).withColumn(
    "Month", month(col("OrderDate"))
).withColumn(
    "DayOfYear", dayofyear(col("OrderDate"))
).withColumn(
    "DayOfWeek", dayofweek(col("OrderDate"))
).withColumn(
    "Quarter", quarter(col("OrderDate"))
).withColumn(
    "IsWeekend", when(dayofweek(col("OrderDate")).isin([1, 7]), 1).otherwise(0)
).withColumn(
    "IsMonthEnd", when(col("OrderDate") == last_day(col("OrderDate")), 1).otherwise(0)
).withColumn(
    "IsQuarterEnd", 
    when(month(col("OrderDate")).isin([3, 6, 9, 12]) & 
         (col("OrderDate") == last_day(col("OrderDate"))), 1).otherwise(0)
)

# Add moving averages and lag features
window_7d = Window.partitionBy("Category", "Region").orderBy("OrderDate").rowsBetween(-6, 0)
window_30d = Window.partitionBy("Category", "Region").orderBy("OrderDate").rowsBetween(-29, 0)
window_lag = Window.partitionBy("Category", "Region").orderBy("OrderDate")

df_ml_ready = df_ml_features.withColumn(
    "Revenue_7DayMA", avg("DailyRevenue").over(window_7d)
).withColumn(
    "Revenue_30DayMA", avg("DailyRevenue").over(window_30d)
).withColumn(
    "Revenue_Lag1", lag("DailyRevenue", 1).over(window_lag)
).withColumn(
    "Revenue_Lag7", lag("DailyRevenue", 7).over(window_lag)
).withColumn(
    "Revenue_Lag30", lag("DailyRevenue", 30).over(window_lag)
).withColumn(
    # Growth rates
    "Revenue_Growth_1D",
    when(col("Revenue_Lag1") > 0, 
         (col("DailyRevenue") - col("Revenue_Lag1")) / col("Revenue_Lag1") * 100)
    .otherwise(0)
).withColumn(
    "Revenue_Growth_7D",
    when(col("Revenue_Lag7") > 0,
         (col("DailyRevenue") - col("Revenue_Lag7")) / col("Revenue_Lag7") * 100)
    .otherwise(0)
).withColumn(
    "ProcessedTimestamp", current_timestamp()
)

print("✅ ML-Ready Time Series Dataset created")
print("\nFeature Summary:")
print(f"Total records: {df_ml_ready.count():,}")
print(f"Date range: {df_ml_ready.agg(min('OrderDate'), max('OrderDate')).collect()[0]}")
print(f"Categories: {df_ml_ready.select('Category').distinct().count()}")
print(f"Regions: {df_ml_ready.select('Region').distinct().count()}")

# Management Scorecard - Salesperson Performance with Target Achievement
df_salesperson_scorecard = df_sales.join(
    df_salesperson, df_sales.EmployeeKey == df_salesperson.EmployeeKey
).join(
    df_targets, df_salesperson.EmployeeID == df_targets.EmployeeID, "left"
).join(
    df_region, "SalesTerritoryKey"
).select(
    col("EmployeeID"),
    col("Salesperson").alias("SalespersonName"),
    col("Title"),
    col("Region"),
    col("Country"),
    year(col("OrderDate")).alias("Year"),
    month(col("OrderDate")).alias("Month"),
    col("Sales").alias("Revenue"),
    col("Target"),
    col("TargetMonth")
).filter(
    # Align sales month with target month
    (year(col("OrderDate")) == year(col("TargetMonth"))) &
    (month(col("OrderDate")) == month(col("TargetMonth")))
).groupBy(
    "EmployeeID", "SalespersonName", "Title", "Region", "Country", "Year", "Month"
).agg(
    sum("Revenue").alias("ActualSales"),
    first("Target").alias("MonthlyTarget"),
    count("*").alias("OrdersCount")
).withColumn(
    # 🎯 PERFORMANCE METRICS
    "TargetAchievement",
    when(col("MonthlyTarget") > 0, col("ActualSales") / col("MonthlyTarget") * 100)
    .otherwise(0)
).withColumn(
    "SalesVariance", col("ActualSales") - col("MonthlyTarget")
).withColumn(
    "PerformanceRating",
    when(col("TargetAchievement") >= 120, "Exceeds")
    .when(col("TargetAchievement") >= 100, "Meets")
    .when(col("TargetAchievement") >= 80, "Below")
    .when(col("TargetAchievement") >= 50, "Poor")
    .otherwise("Critical")
).withColumn(
    # 🏆 RANKINGS
    "SalesRank",
    row_number().over(Window.partitionBy("Year", "Month").orderBy(desc("ActualSales")))
).withColumn(
    "AchievementRank",
    row_number().over(Window.partitionBy("Year", "Month").orderBy(desc("TargetAchievement")))
).withColumn(
    # 💰 COMPENSATION INDICATORS
    "BonusEligible",
    when(col("TargetAchievement") >= 100, "Yes").otherwise("No")
).withColumn(
    "CommissionTier",
    when(col("TargetAchievement") >= 150, "Tier 1 - 15%")
    .when(col("TargetAchievement") >= 120, "Tier 2 - 12%")
    .when(col("TargetAchievement") >= 100, "Tier 3 - 10%")
    .when(col("TargetAchievement") >= 80, "Tier 4 - 8%")
    .otherwise("Base - 5%")
).withColumn(
    "ProcessedTimestamp", current_timestamp()
)

print("✅ Sales Performance Scorecard created")
print("\nPerformance Distribution:")
df_salesperson_scorecard.groupBy("PerformanceRating").agg(
    count("*").alias("Count"),
    avg("TargetAchievement").alias("AvgAchievement"),
    avg("ActualSales").alias("AvgSales")
).orderBy(desc("Count")).show()

print("\nTop Performers (Current Month):")
df_salesperson_scorecard.filter(col("SalesRank") <= 5) \
    .select("SalespersonName", "Region", "ActualSales", "TargetAchievement", "PerformanceRating") \
    .show()

# Save all Gold layer tables for consumption
gold_tables = [
    (df_executive_kpis, "executive_kpis", ["Year", "Month"]),
    (df_customer_segments, "customer_rfm_segments", None),
    (df_abc_analysis, "product_abc_analysis", ["ABC_Category"]),
    (df_ml_ready, "ml_time_series_features", ["Year", "Month"]),
    (df_salesperson_scorecard, "sales_performance_scorecard", ["Year", "Month"])
]

for df, table_name, partition_cols in gold_tables:
    print(f"\n💾 Saving {table_name}...")
    
    writer = df.write.format("delta").mode("overwrite").option("mergeSchema", "true")
    
    if partition_cols:
        writer = writer.partitionBy(*partition_cols)
        print(f"   📁 Partitioned by: {partition_cols}")
    
    writer.saveAsTable(f"{catalog}.{gold_schema}.{table_name}")
    
    # Optimize and analyze
    spark.sql(f"OPTIMIZE {catalog}.{gold_schema}.{table_name}")
    spark.sql(f"ANALYZE TABLE {catalog}.{gold_schema}.{table_name} COMPUTE STATISTICS")
    
    print(f"   ✅ {table_name} saved and optimized")

print("\n🎉 Gold Layer Analytics Tables Ready!")
print("\n📊 Available for:")
print("   • Business Intelligence Dashboards")
print("   • Machine Learning Models")
print("   • Executive Reporting")
print("   • Data Science Analysis")
print("   • Advanced Analytics")