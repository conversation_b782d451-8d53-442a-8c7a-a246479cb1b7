{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Adventure Works Gold Layer - Business Analytics & ML Features\n", "\n", "This notebook creates **Gold Layer** datasets optimized for:\n", "- 📊 **Business Intelligence** dashboards\n", "- 🤖 **Machine Learning** feature engineering\n", "- 📈 **Advanced Analytics** for data scientists\n", "- 🎯 **Executive Reporting** and KPIs\n", "\n", "## Gold Layer Principles:\n", "1. **Business-focused aggregations** and metrics\n", "2. **ML-ready feature sets** with proper encoding\n", "3. **Denormalized tables** for fast querying\n", "4. **Time-series data** for forecasting\n", "5. **KPI calculations** for executive dashboards"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql.functions import *\n", "from pyspark.sql.types import *\n", "from pyspark.sql.window import Window\n", "from pyspark.ml.feature import VectorAssembler, StandardScaler\n", "from pyspark.ml.stat import Correlation\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration\n", "catalog = \"adventworks\"\n", "bronze_schema = \"bronze_schema\"\n", "silver_schema = \"silver_schema\"\n", "gold_schema = \"gold_schema\"\n", "\n", "spark.sql(f\"USE CATALOG {catalog}\")\n", "spark.sql(f\"CREATE SCHEMA IF NOT EXISTS {catalog}.{gold_schema}\")\n", "\n", "print(f\"✅ Gold schema {catalog}.{gold_schema} ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Executive KPI Dashboard - Monthly Business Metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read from Silver layer (cleaned data)\n", "df_sales = spark.table(f\"{catalog}.{bronze_schema}.sales\")\n", "df_product = spark.table(f\"{catalog}.{bronze_schema}.product\")\n", "df_targets = spark.table(f\"{catalog}.{bronze_schema}.targets\")\n", "df_salesperson = spark.table(f\"{catalog}.{bronze_schema}.salesperson\")\n", "df_region = spark.table(f\"{catalog}.{bronze_schema}.region\")\n", "\n", "# Executive KPI Dashboard - Monthly aggregated metrics\n", "df_executive_kpis = df_sales.join(df_product, \"ProductKey\") \\\n", "    .join(df_region, \"SalesTerritoryKey\") \\\n", "    .select(\n", "        year(col(\"OrderDate\")).alias(\"Year\"),\n", "        month(col(\"OrderDate\")).alias(\"Month\"),\n", "        col(\"Region\"),\n", "        col(\"Country\"),\n", "        col(\"Category\"),\n", "        col(\"Sales\").alias(\"Revenue\"),\n", "        col(\"Quantity\"),\n", "        col(\"Standard_Cost\"),\n", "        col(\"ResellerKey\")\n", "    ).groupBy(\"Year\", \"Month\", \"Region\", \"Country\", \"Category\").agg(\n", "        # 💰 FINANCIAL KPIs\n", "        sum(\"Revenue\").alias(\"TotalRevenue\"),\n", "        sum(\"Quantity\").alias(\"TotalUnitsSold\"),\n", "        sum(col(\"Quantity\") * col(\"Standard_Cost\")).alias(\"TotalCOGS\"),\n", "        count(\"*\").alias(\"TotalOrders\"),\n", "        countDistinct(\"Reseller<PERSON><PERSON>\").alias(\"ActiveCustomers\"),\n", "        \n", "        # 📊 BUSINESS METRICS\n", "        avg(\"Revenue\").alias(\"AvgOrderValue\"),\n", "        avg(\"Quantity\").alias(\"AvgOrderSize\")\n", "    ).withColumn(\n", "        # 💡 CALCULATED KPIs\n", "        \"GrossProfit\", col(\"TotalRevenue\") - col(\"TotalCOGS\")\n", "    ).withColumn(\n", "        \"GrossProfitMargin\", \n", "        when(col(\"TotalRevenue\") > 0, col(\"GrossProfit\") / col(\"TotalRevenue\") * 100)\n", "        .otherwise(0)\n", "    ).withColumn(\n", "        \"RevenuePerCustomer\",\n", "        when(col(\"ActiveCustomers\") > 0, col(\"TotalRevenue\") / col(\"ActiveCustomers\"))\n", "        .otherwise(0)\n", "    ).withColumn(\n", "        \"OrdersPerCustomer\",\n", "        when(col(\"ActiveCustomers\") > 0, col(\"TotalOrders\") / col(\"ActiveCustomers\"))\n", "        .otherwise(0)\n", "    ).withColumn(\n", "        # 📅 TIME DIMENSIONS\n", "        \"Year<PERSON><PERSON>h\", concat(col(\"Year\"), lit(\"-\"), lpad(col(\"Month\"), 2, \"0\"))\n", "    ).withColumn(\n", "        \"Quarter\", \n", "        when(col(\"Month\").isin([1,2,3]), \"Q1\")\n", "        .when(col(\"Month\").isin([4,5,6]), \"Q2\")\n", "        .when(col(\"Month\").isin([7,8,9]), \"Q3\")\n", "        .otherwise(\"Q4\")\n", "    ).withColumn(\n", "        \"ProcessedTimestamp\", current_timestamp()\n", "    )\n", "\n", "print(\"✅ Executive KPI Dashboard created\")\n", "df_executive_kpis.orderBy(desc(\"Year\"), desc(\"Month\"), desc(\"TotalRevenue\")).show(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Customer Analytics - RFM Analysis for Marketing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Customer RFM Analysis (Recency, Frequency, Monetary) - Gold standard for marketing\n", "analysis_date = spark.sql(\"SELECT max(OrderDate) as max_date FROM adventworks.bronze_schema.sales\").collect()[0]['max_date']\n", "\n", "df_customer_rfm = df_sales.groupBy(\"ResellerKey\").agg(\n", "    # 📅 RECENCY (days since last purchase)\n", "    datediff(lit(analysis_date), max(\"OrderDate\")).alias(\"Recency\"),\n", "    \n", "    # 🔄 FREQUENCY (number of purchases)\n", "    count(\"*\").alias(\"Frequency\"),\n", "    \n", "    # 💰 MONETARY (total spent)\n", "    sum(\"Sales\").alias(\"Monetary\"),\n", "    \n", "    # 📊 ADDITIONAL METRICS\n", "    avg(\"Sales\").alias(\"AvgOrderValue\"),\n", "    countDistinct(\"ProductKey\").alias(\"ProductDiversity\"),\n", "    min(\"OrderDate\").alias(\"FirstPurchaseDate\"),\n", "    max(\"OrderDate\").alias(\"LastPurchaseDate\")\n", ").withColumn(\n", "    \"CustomerLifespanDays\",\n", "    datediff(col(\"LastPurchaseDate\"), col(\"FirstPurchaseDate\"))\n", ")\n", "\n", "# Calculate RFM Scores (1-5 scale)\n", "recency_quantiles = df_customer_rfm.approxQuantile(\"Recency\", [0.2, 0.4, 0.6, 0.8], 0.01)\n", "frequency_quantiles = df_customer_rfm.approxQuantile(\"Frequency\", [0.2, 0.4, 0.6, 0.8], 0.01)\n", "monetary_quantiles = df_customer_rfm.approxQuantile(\"Monetary\", [0.2, 0.4, 0.6, 0.8], 0.01)\n", "\n", "df_customer_segments = df_customer_rfm.withColumn(\n", "    \"R_Score\",\n", "    when(col(\"Recency\") <= recency_quantiles[0], 5)\n", "    .when(col(\"Recency\") <= recency_quantiles[1], 4)\n", "    .when(col(\"Recency\") <= recency_quantiles[2], 3)\n", "    .when(col(\"Recency\") <= recency_quantiles[3], 2)\n", "    .otherwise(1)\n", ").withColumn(\n", "    \"F_Score\",\n", "    when(col(\"Frequency\") >= frequency_quantiles[3], 5)\n", "    .when(col(\"Frequency\") >= frequency_quantiles[2], 4)\n", "    .when(col(\"Frequency\") >= frequency_quantiles[1], 3)\n", "    .when(col(\"Frequency\") >= frequency_quantiles[0], 2)\n", "    .otherwise(1)\n", ").withColumn(\n", "    \"M_Score\",\n", "    when(col(\"Monetary\") >= monetary_quantiles[3], 5)\n", "    .when(col(\"Monetary\") >= monetary_quantiles[2], 4)\n", "    .when(col(\"Monetary\") >= monetary_quantiles[1], 3)\n", "    .when(col(\"Monetary\") >= monetary_quantiles[0], 2)\n", "    .otherwise(1)\n", ").withColumn(\n", "    \"RFM_Score\", concat(col(\"R_Score\"), col(\"F_Score\"), col(\"M_Score\"))\n", ").withColumn(\n", "    # 🎯 BUSINESS SEGMENTS\n", "    \"CustomerSegment\",\n", "    when((col(\"R_Score\") >= 4) & (col(\"F_Score\") >= 4) & (col(\"M_Score\") >= 4), \"Champions\")\n", "    .when((col(\"R_Score\") >= 3) & (col(\"F_Score\") >= 3) & (col(\"M_Score\") >= 4), \"Loyal Customers\")\n", "    .when((col(\"R_Score\") >= 4) & (col(\"F_Score\") <= 2), \"New Customers\")\n", "    .when((col(\"R_Score\") >= 3) & (col(\"F_Score\") >= 3) & (col(\"M_Score\") <= 3), \"Potential Loyalists\")\n", "    .when((col(\"R_Score\") <= 2) & (col(\"F_Score\") >= 3) & (col(\"M_Score\") >= 3), \"At Risk\")\n", "    .when((col(\"R_Score\") <= 2) & (col(\"F_Score\") <= 2) & (col(\"M_Score\") >= 4), \"Can't Lose Them\")\n", "    .when((col(\"R_Score\") <= 2) & (col(\"F_Score\") <= 2) & (col(\"M_Score\") <= 2), \"Lost Customers\")\n", "    .otherwise(\"Others\")\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ Customer RFM Analysis completed\")\n", "print(\"\\nCustomer Segments Distribution:\")\n", "df_customer_segments.groupBy(\"CustomerSegment\").agg(\n", "    count(\"*\").alias(\"CustomerCount\"),\n", "    avg(\"Monetary\").alias(\"AvgMonetaryValue\")\n", ").orderBy(desc(\"CustomerCount\")).show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Product Performance Analytics - ABC Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Product ABC Analysis for Inventory Management\n", "df_product_performance = df_sales.join(df_product, \"ProductKey\").groupBy(\n", "    \"ProductKey\", \"Product\", \"Category\", \"Subcategory\", \"Standard_Cost\"\n", ").agg(\n", "    sum(\"Sales\").alias(\"TotalRevenue\"),\n", "    sum(\"Quantity\").alias(\"TotalQuantitySold\"),\n", "    count(\"*\").alias(\"TotalOrders\"),\n", "    countDistinct(\"Reseller<PERSON><PERSON>\").alias(\"UniqueCustomers\"),\n", "    avg(\"Sales\").alias(\"AvgOrderValue\"),\n", "    stddev(\"Sales\").alias(\"RevenueStdDev\")\n", ").withColumn(\n", "    \"TotalCost\", col(\"TotalQuantitySold\") * col(\"Standard_Cost\")\n", ").withColumn(\n", "    \"GrossProfit\", col(\"TotalRevenue\") - col(\"TotalCost\")\n", ").withColumn(\n", "    \"ProfitMargin\",\n", "    when(col(\"TotalRevenue\") > 0, col(\"GrossProfit\") / col(\"TotalRevenue\") * 100)\n", "    .otherwise(0)\n", ")\n", "\n", "# Calculate cumulative revenue percentage for ABC classification\n", "total_revenue = df_product_performance.agg(sum(\"TotalRevenue\")).collect()[0][0]\n", "\n", "window_spec = Window.orderBy(desc(\"TotalRevenue\"))\n", "df_abc_analysis = df_product_performance.withColumn(\n", "    \"RevenueRank\", row_number().over(window_spec)\n", ").withColumn(\n", "    \"CumulativeRevenue\", sum(\"TotalRevenue\").over(window_spec.rowsBetween(Window.unboundedPreceding, 0))\n", ").withColumn(\n", "    \"CumulativeRevenuePercent\", col(\"CumulativeRevenue\") / total_revenue * 100\n", ").withColumn(\n", "    # 📊 ABC CLASSIFICATION\n", "    \"ABC_Category\",\n", "    when(col(\"CumulativeRevenuePercent\") <= 80, \"A - High Value\")\n", "    .when(col(\"CumulativeRevenuePercent\") <= 95, \"B - Medium Value\")\n", "    .otherwise(\"C - Low Value\")\n", ").withColumn(\n", "    # 🎯 BUSINESS RECOMMENDATIONS\n", "    \"InventoryStrategy\",\n", "    when(col(\"ABC_Category\") == \"A - High Value\", \"High Stock - Frequent Review\")\n", "    .when(col(\"ABC_Category\") == \"B - Medium Value\", \"Moderate Stock - Regular Review\")\n", "    .otherwise(\"Low Stock - Periodic Review\")\n", ").withColumn(\n", "    \"MarketingPriority\",\n", "    when((col(\"ABC_Category\") == \"A - High Value\") & (col(\"ProfitMargin\") >= 30), \"High Priority\")\n", "    .when(col(\"ABC_Category\") == \"A - High Value\", \"Medium Priority\")\n", "    .otherwise(\"Low Priority\")\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ Product ABC Analysis completed\")\n", "print(\"\\nABC Distribution:\")\n", "df_abc_analysis.groupBy(\"ABC_Category\").agg(\n", "    count(\"*\").alias(\"ProductCount\"),\n", "    sum(\"TotalRevenue\").alias(\"CategoryRevenue\"),\n", "    avg(\"ProfitMargin\").alias(\"AvgProfitMargin\")\n", ").show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Time Series Data for Forecasting & ML"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Time Series Dataset for Data Scientists and ML Engineers\n", "df_time_series = df_sales.join(df_product, \"ProductKey\") \\\n", "    .join(df_region, \"SalesTerritoryKey\") \\\n", "    .select(\n", "        col(\"OrderDate\"),\n", "        col(\"Sales\").alias(\"Revenue\"),\n", "        col(\"Quantity\"),\n", "        col(\"Category\"),\n", "        col(\"Region\"),\n", "        col(\"Country\")\n", "    ).groupBy(\"OrderDate\", \"Category\", \"Region\", \"Country\").agg(\n", "        sum(\"Revenue\").alias(\"DailyRevenue\"),\n", "        sum(\"Quantity\").alias(\"DailyQuantity\"),\n", "        count(\"*\").alias(\"DailyOrders\")\n", "    )\n", "\n", "# Add time-based features for ML\n", "df_ml_features = df_time_series.withColumn(\n", "    \"Year\", year(col(\"OrderDate\"))\n", ").withColumn(\n", "    \"Month\", month(col(\"OrderDate\"))\n", ").withColumn(\n", "    \"DayOfYear\", dayofyear(col(\"OrderDate\"))\n", ").withColumn(\n", "    \"DayOfWeek\", dayofweek(col(\"OrderDate\"))\n", ").withColumn(\n", "    \"Quarter\", quarter(col(\"OrderDate\"))\n", ").withColumn(\n", "    \"IsWeekend\", when(dayofweek(col(\"OrderDate\")).isin([1, 7]), 1).otherwise(0)\n", ").withColumn(\n", "    \"IsMonthEnd\", when(col(\"OrderDate\") == last_day(col(\"OrderDate\")), 1).otherwise(0)\n", ").withColumn(\n", "    \"IsQuarterEnd\", \n", "    when(month(col(\"OrderDate\")).isin([3, 6, 9, 12]) & \n", "         (col(\"OrderDate\") == last_day(col(\"OrderDate\"))), 1).otherwise(0)\n", ")\n", "\n", "# Add moving averages and lag features\n", "window_7d = Window.partitionBy(\"Category\", \"Region\").orderBy(\"OrderDate\").rowsBetween(-6, 0)\n", "window_30d = Window.partitionBy(\"Category\", \"Region\").orderBy(\"OrderDate\").rowsBetween(-29, 0)\n", "window_lag = Window.partitionBy(\"Category\", \"Region\").orderBy(\"OrderDate\")\n", "\n", "df_ml_ready = df_ml_features.withColumn(\n", "    \"Revenue_7DayMA\", avg(\"DailyRevenue\").over(window_7d)\n", ").withColumn(\n", "    \"Revenue_30DayMA\", avg(\"DailyRevenue\").over(window_30d)\n", ").withColumn(\n", "    \"Revenue_Lag1\", lag(\"DailyRevenue\", 1).over(window_lag)\n", ").withColumn(\n", "    \"Revenue_Lag7\", lag(\"DailyRevenue\", 7).over(window_lag)\n", ").withColumn(\n", "    \"Revenue_Lag30\", lag(\"DailyRevenue\", 30).over(window_lag)\n", ").withColumn(\n", "    # Growth rates\n", "    \"Revenue_Growth_1D\",\n", "    when(col(\"Revenue_Lag1\") > 0, \n", "         (col(\"DailyRevenue\") - col(\"Revenue_Lag1\")) / col(\"Revenue_Lag1\") * 100)\n", "    .otherwise(0)\n", ").withColumn(\n", "    \"Revenue_Growth_7D\",\n", "    when(col(\"Revenue_Lag7\") > 0,\n", "         (col(\"DailyRevenue\") - col(\"Revenue_Lag7\")) / col(\"Revenue_Lag7\") * 100)\n", "    .otherwise(0)\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ ML-Ready Time Series Dataset created\")\n", "print(\"\\nFeature Summary:\")\n", "print(f\"Total records: {df_ml_ready.count():,}\")\n", "print(f\"Date range: {df_ml_ready.agg(min('OrderDate'), max('OrderDate')).collect()[0]}\")\n", "print(f\"Categories: {df_ml_ready.select('Category').distinct().count()}\")\n", "print(f\"Regions: {df_ml_ready.select('Region').distinct().count()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Sales Performance Scorecard for Management"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Management Scorecard - Salesperson Performance with Target Achievement\n", "df_salesperson_scorecard = df_sales.join(\n", "    df_salesperson, df_sales.EmployeeKey == df_salesperson.EmployeeKey\n", ").join(\n", "    df_targets, df_salesperson.EmployeeID == df_targets.EmployeeID, \"left\"\n", ").join(\n", "    df_region, \"SalesTerritoryKey\"\n", ").select(\n", "    col(\"EmployeeID\"),\n", "    col(\"Salesperson\").alias(\"Salesperson<PERSON><PERSON>\"),\n", "    col(\"Title\"),\n", "    col(\"Region\"),\n", "    col(\"Country\"),\n", "    year(col(\"OrderDate\")).alias(\"Year\"),\n", "    month(col(\"OrderDate\")).alias(\"Month\"),\n", "    col(\"Sales\").alias(\"Revenue\"),\n", "    col(\"Target\"),\n", "    col(\"TargetMonth\")\n", ").filter(\n", "    # Align sales month with target month\n", "    (year(col(\"OrderDate\")) == year(col(\"TargetMonth\"))) &\n", "    (month(col(\"OrderDate\")) == month(col(\"TargetMonth\")))\n", ").groupBy(\n", "    \"EmployeeID\", \"SalespersonName\", \"Title\", \"Region\", \"Country\", \"Year\", \"Month\"\n", ").agg(\n", "    sum(\"Revenue\").alias(\"ActualSales\"),\n", "    first(\"Target\").alias(\"MonthlyTarget\"),\n", "    count(\"*\").alias(\"OrdersCount\")\n", ").withColumn(\n", "    # 🎯 PERFORMANCE METRICS\n", "    \"TargetAchievement\",\n", "    when(col(\"MonthlyTarget\") > 0, col(\"ActualSales\") / col(\"MonthlyTarget\") * 100)\n", "    .otherwise(0)\n", ").withColumn(\n", "    \"SalesVariance\", col(\"ActualSales\") - col(\"MonthlyTarget\")\n", ").withColumn(\n", "    \"PerformanceRating\",\n", "    when(col(\"TargetAchievement\") >= 120, \"Exceeds\")\n", "    .when(col(\"TargetAchievement\") >= 100, \"Meets\")\n", "    .when(col(\"TargetAchievement\") >= 80, \"Below\")\n", "    .when(col(\"TargetAchievement\") >= 50, \"Poor\")\n", "    .otherwise(\"Critical\")\n", ").withColumn(\n", "    # 🏆 RANKINGS\n", "    \"SalesRank\",\n", "    row_number().over(Window.partitionBy(\"Year\", \"Month\").orderBy(desc(\"ActualSales\")))\n", ").withColumn(\n", "    \"AchievementRank\",\n", "    row_number().over(Window.partitionBy(\"Year\", \"Month\").orderBy(desc(\"TargetAchievement\")))\n", ").withColumn(\n", "    # 💰 COMPENSATION INDICATORS\n", "    \"BonusEligible\",\n", "    when(col(\"TargetAchievement\") >= 100, \"Yes\").otherwise(\"No\")\n", ").withColumn(\n", "    \"CommissionTier\",\n", "    when(col(\"TargetAchievement\") >= 150, \"Tier 1 - 15%\")\n", "    .when(col(\"TargetAchievement\") >= 120, \"Tier 2 - 12%\")\n", "    .when(col(\"TargetAchievement\") >= 100, \"Tier 3 - 10%\")\n", "    .when(col(\"TargetAchievement\") >= 80, \"Tier 4 - 8%\")\n", "    .otherwise(\"Base - 5%\")\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ Sales Performance Scorecard created\")\n", "print(\"\\nPerformance Distribution:\")\n", "df_salesperson_scorecard.groupBy(\"PerformanceRating\").agg(\n", "    count(\"*\").alias(\"Count\"),\n", "    avg(\"TargetAchievement\").alias(\"AvgAchievement\"),\n", "    avg(\"ActualSales\").alias(\"AvgSales\")\n", ").orderBy(desc(\"Count\")).show()\n", "\n", "print(\"\\nTop Performers (Current Month):\")\n", "df_salesperson_scorecard.filter(col(\"SalesRank\") <= 5) \\\n", "    .select(\"SalespersonName\", \"Region\", \"ActualSales\", \"TargetAchievement\", \"PerformanceRating\") \\\n", "    .show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Save Gold Layer Tables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save all Gold layer tables for consumption\n", "gold_tables = [\n", "    (df_executive_kpis, \"executive_kpis\", [\"Year\", \"Month\"]),\n", "    (df_customer_segments, \"customer_rfm_segments\", None),\n", "    (df_abc_analysis, \"product_abc_analysis\", [\"ABC_Category\"]),\n", "    (df_ml_ready, \"ml_time_series_features\", [\"Year\", \"Month\"]),\n", "    (df_salesperson_scorecard, \"sales_performance_scorecard\", [\"Year\", \"Month\"])\n", "]\n", "\n", "for df, table_name, partition_cols in gold_tables:\n", "    print(f\"\\n💾 Saving {table_name}...\")\n", "    \n", "    writer = df.write.format(\"delta\").mode(\"overwrite\").option(\"mergeSchema\", \"true\")\n", "    \n", "    if partition_cols:\n", "        writer = writer.partitionBy(*partition_cols)\n", "        print(f\"   📁 Partitioned by: {partition_cols}\")\n", "    \n", "    writer.saveAsTable(f\"{catalog}.{gold_schema}.{table_name}\")\n", "    \n", "    # Optimize and analyze\n", "    spark.sql(f\"OPTIMIZE {catalog}.{gold_schema}.{table_name}\")\n", "    spark.sql(f\"ANALYZE TABLE {catalog}.{gold_schema}.{table_name} COMPUTE STATISTICS\")\n", "    \n", "    print(f\"   ✅ {table_name} saved and optimized\")\n", "\n", "print(\"\\n🎉 Gold Layer Analytics Tables Ready!\")\n", "print(\"\\n📊 Available for:\")\n", "print(\"   • Business Intelligence Dashboards\")\n", "print(\"   • Machine Learning Models\")\n", "print(\"   • Executive Reporting\")\n", "print(\"   • Data Science Analysis\")\n", "print(\"   • Advanced Analytics\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}