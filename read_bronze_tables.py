# Adventure Works Bronze Layer Data Reader
# This script reads all parquet files from the bronze folder in ADLS

from pyspark.sql import SparkSession
from pyspark.sql.functions import *

# Initialize Spark session (if not already available)
# spark = SparkSession.builder.appName("AdventureWorksBronzeReader").getOrCreate()

# ADLS file path configuration
filePath = r"abfss://<EMAIL>/"
bronze_path = filePath + "bronze/"

# Catalog and schema configuration
catalog = "adventure_works"
schema = "bronze_schema"

# List of all tables in bronze layer
bronze_tables = [
    "product",
    "region", 
    "reseller",
    "sales",
    "salesperson",
    "salespersonregion",
    "targets"
]

# Dictionary to store all DataFrames
dataframes = {}

print("Reading Adventure Works Bronze Layer Tables...")
print("=" * 50)

# Read all parquet files from bronze folder
for table_name in bronze_tables:
    try:
        print(f"Reading {table_name} table...")
        
        # Read parquet file
        df = (spark.read
              .format('parquet')
              .load(bronze_path + table_name)
        )
        
        # Store DataFrame in dictionary
        dataframes[f"df_{table_name}"] = df
        
        # Display basic info about the table
        print(f"✓ {table_name.upper()} - Rows: {df.count()}, Columns: {len(df.columns)}")
        print(f"  Columns: {', '.join(df.columns)}")
        
        # Show first few rows
        print(f"  Sample data:")
        df.show(5, truncate=False)
        print("-" * 50)
        
    except Exception as e:
        print(f"✗ Error reading {table_name}: {str(e)}")
        print("-" * 50)

print("All bronze tables loaded successfully!")
print("\nAvailable DataFrames:")
for df_name in dataframes.keys():
    print(f"- {df_name}")

# Optional: Create temporary views for SQL queries
print("\nCreating temporary views...")
for table_name in bronze_tables:
    df_name = f"df_{table_name}"
    if df_name in dataframes:
        dataframes[df_name].createOrReplaceTempView(f"bronze_{table_name}")
        print(f"✓ Created view: bronze_{table_name}")

print("\nYou can now use these DataFrames:")
print("Example usage:")
print("- dataframes['df_product'].show()")
print("- spark.sql('SELECT * FROM bronze_sales LIMIT 10').show()")
