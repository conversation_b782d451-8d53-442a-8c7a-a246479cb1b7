{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Adventure Works Silver Layer - Corrected Transformations\n", "\n", "This notebook implements Silver Layer transformations using the **actual column names** from your Adventure Works data.\n", "\n", "## Actual Column <PERSON>:\n", "- **Sales**: SalesOrderNumber, OrderDate, ProductKey, ResellerKey, EmployeeKey, SalesTerritoryKey, Quantity, Unit_Price, Sales\n", "- **Reseller**: ResellerKey, Business_Type, Reseller, City, State_Province, Country_Region\n", "- **Salesperson**: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EmployeeID, Salesperson, Title, UPN\n", "- **Product**: ProductKey, Product, Standard_Cost, Color, Subcategory, Category, Background_Color_Format, Font_Color_Format"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "from pyspark.sql.functions import *\n", "from pyspark.sql.types import *\n", "from pyspark.sql.window import Window\n", "import re"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Catalog and schema configuration\n", "catalog = \"adventworks\"\n", "bronze_schema = \"bronze_schema\"\n", "silver_schema = \"silver_schema\"\n", "\n", "spark.sql(f\"USE CATALOG {catalog}\")\n", "spark.sql(f\"USE SCHEMA {bronze_schema}\")\n", "\n", "# Create silver schema if it doesn't exist\n", "spark.sql(f\"CREATE SCHEMA IF NOT EXISTS {catalog}.{silver_schema}\")\n", "\n", "print(f\"Working with catalog: {catalog}\")\n", "print(f\"Bronze schema: {bronze_schema}\")\n", "print(f\"Silver schema: {silver_schema}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Enhanced Product Transformations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read bronze product data\n", "df_product_bronze = spark.table(f\"{catalog}.{bronze_schema}.product\")\n", "\n", "# Enhanced Product transformations\n", "df_product_silver = df_product_bronze.select(\n", "    col(\"ProductKey\"),\n", "    col(\"Product\").alias(\"ProductName\"),\n", "    col(\"Standard_Cost\").alias(\"StandardCost\"),\n", "    col(\"Color\"),\n", "    col(\"Subcategory\"),\n", "    col(\"Category\"),\n", "    \n", "    # 🔍 BUSINESS INTELLIGENCE TRANSFORMATIONS\n", "    \n", "    # Extract size from product name (for bikes, clothing, etc.)\n", "    regexp_extract(col(\"Product\"), r\"(\\d+)\", 1).alias(\"ExtractedSize\"),\n", "    \n", "    # Extract product line/series (HL, ML, LL)\n", "    regexp_extract(col(\"Product\"), r\"^(HL|ML|LL)\", 1).alias(\"ProductLine\"),\n", "    \n", "    # Product tier based on naming convention\n", "    when(col(\"Product\").startswith(\"HL\"), \"High-End\")\n", "    .when(col(\"Product\").startswith(\"ML\"), \"Mid-Range\")\n", "    .when(col(\"Product\").startswith(\"LL\"), \"Entry-Level\")\n", "    .when(col(\"Product\").contains(\"100\"), \"Premium\")\n", "    .when(col(\"Product\").contains(\"200\"), \"Standard\")\n", "    .when(col(\"Product\").contains(\"300\"), \"Basic\")\n", "    .otherwise(\"Unclassified\").alias(\"ProductTier\"),\n", "    \n", "    # Cost categories for pricing strategy\n", "    when(col(\"Standard_Cost\") >= 1000, \"High-Cost\")\n", "    .when(col(\"Standard_Cost\") >= 100, \"Medium-Cost\")\n", "    .when(col(\"Standard_Cost\") >= 10, \"Low-Cost\")\n", "    .otherwise(\"Ultra-Low-Cost\").alias(\"CostCategor<PERSON>\"),\n", "    \n", "    # Gender targeting (for clothing/bikes)\n", "    when(col(\"Product\").contains(\"-W \") | col(\"Product\").contains(\"Women\"), \"Women\")\n", "    .when(col(\"Product\").contains(\"Men\"), \"Men\")\n", "    .otherwise(\"Unisex\").alias(\"TargetG<PERSON>\"),\n", "    \n", "    # Seasonal products\n", "    when(col(\"Subcategory\").isin([\"Gloves\", \"Tights\", \"Vests\"]), \"Winter\")\n", "    .when(col(\"Subcategory\").isin([\"<PERSON>s\", \"Jerseys\"]), \"Summer\")\n", "    .otherwise(\"All-Season\").alias(\"Seasonality\"),\n", "    \n", "    # Product complexity (number of variants)\n", "    when(col(\"Product\").contains(\"Frame\") | col(\"Product\").contains(\"Bike\"), \"Complex\")\n", "    .when(col(\"Category\") == \"Components\", \"Moderate\")\n", "    .otherwise(\"Simple\").alias(\"ProductComplexity\"),\n", "    \n", "    # Color standardization and grouping\n", "    when(col(\"Color\").isin([\"<PERSON>\", \"White\", \"<PERSON>\", \"Grey\"]), \"Neutral\")\n", "    .when(col(\"Color\").isin([\"Red\", \"Blue\", \"Yellow\"]), \"Primary\")\n", "    .when(col(\"Color\") == \"Multi\", \"Multi-Color\")\n", "    .when(col(\"Color\") == \"NA\", \"No Color\")\n", "    .otherwise(\"Other\").alias(\"ColorGroup\"),\n", "    \n", "    # Existing color formatting (keeping your good work!)\n", "    col(\"Background_Color_Format\"),\n", "    col(\"Font_Color_Format\"),\n", "    \n", "    # Data quality flags\n", "    when(col(\"Standard_Cost\").isNull() | (col(\"Standard_Cost\") <= 0), \"Invalid Cost\")\n", "    .when(col(\"Color\").is<PERSON><PERSON>(), \"Missing Color\")\n", "    .otherwise(\"Valid\").alias(\"DataQualityFlag\"),\n", "    \n", "    # Metadata\n", "    current_timestamp().alias(\"ProcessedTimestamp\"),\n", "    lit(\"silver_transformation_v1\").alias(\"TransformationVersion\")\n", ")\n", "\n", "print(\"✅ Product Silver transformations completed\")\n", "df_product_silver.show(5, truncate=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Enhanced Sales Transformations (Corrected Column Names)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read bronze sales data\n", "df_sales_bronze = spark.table(f\"{catalog}.{bronze_schema}.sales\")\n", "\n", "# Enhanced Sales transformations with CORRECT column names\n", "df_sales_silver = df_sales_bronze.select(\n", "    col(\"SalesOrderNumber\"),\n", "    col(\"OrderDate\"),\n", "    col(\"ProductKey\"),\n", "    col(\"ResellerKey\"),\n", "    col(\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\").alias(\"Salesperson<PERSON><PERSON>\"),  # Map EmployeeKey to Salesperson<PERSON><PERSON> for consistency\n", "    col(\"SalesTerritoryKey\"),\n", "    col(\"Quantity\").alias(\"OrderQuantity\"),\n", "    col(\"Unit_Price\").alias(\"UnitPrice\"),\n", "    col(\"Sales\").alias(\"SalesAmount\"),\n", "    \n", "    # 💰 REVENUE & PROFITABILITY ANALYSIS\n", "    \n", "    # Total order value (Sales column already includes total)\n", "    col(\"Sales\").alias(\"TotalOrderValue\"),\n", "    \n", "    # Revenue per unit\n", "    when(col(\"Quantity\") > 0, col(\"Sales\") / col(\"Quantity\"))\n", "    .otherwise(0).alias(\"RevenuePerUnit\"),\n", "    \n", "    # Order size categories\n", "    when(col(\"Quantity\") >= 10, \"Bulk\")\n", "    .when(col(\"Quantity\") >= 5, \"Medium\")\n", "    .when(col(\"Quantity\") >= 2, \"Small\")\n", "    .otherwise(\"Single\").alias(\"OrderSizeCategory\"),\n", "    \n", "    # Revenue tiers\n", "    when(col(\"Sales\") >= 5000, \"High-Value\")\n", "    .when(col(\"Sales\") >= 1000, \"Medium-Value\")\n", "    .when(col(\"Sales\") >= 100, \"Low-Value\")\n", "    .otherwise(\"Minimal-Value\").alias(\"RevenueCategory\"),\n", "    \n", "    # 📅 TIME-BASED ANALYSIS\n", "    \n", "    # Extract date components\n", "    year(col(\"OrderDate\")).alias(\"OrderYear\"),\n", "    month(col(\"OrderDate\")).alias(\"Order<PERSON><PERSON><PERSON>\"),\n", "    quarter(col(\"OrderDate\")).alias(\"OrderQuarter\"),\n", "    dayofweek(col(\"OrderDate\")).alias(\"OrderDayOfWeek\"),\n", "    weekofyear(col(\"OrderDate\")).alias(\"OrderWeek\"),\n", "    \n", "    # Business day classification\n", "    when(dayofweek(col(\"OrderDate\")).isin([1, 7]), \"Weekend\")\n", "    .otherwise(\"Weekday\").alias(\"DayType\"),\n", "    \n", "    # Season classification\n", "    when(month(col(\"OrderDate\")).isin([12, 1, 2]), \"Winter\")\n", "    .when(month(col(\"OrderDate\")).isin([3, 4, 5]), \"Spring\")\n", "    .when(month(col(\"OrderDate\")).isin([6, 7, 8]), \"Summer\")\n", "    .otherwise(\"Fall\").alias(\"Season\"),\n", "    \n", "    # Month name for reporting\n", "    date_format(col(\"OrderDate\"), \"MMMM\").alias(\"OrderMonthName\"),\n", "    \n", "    # Days since epoch (for trend analysis)\n", "    datediff(col(\"OrderDate\"), lit(\"1900-01-01\")).alias(\"DaysSinceEpoch\"),\n", "    \n", "    # 🎯 BUSINESS METRICS\n", "    \n", "    # Discount calculation (if UnitPrice vs Sales/Quantity differs)\n", "    when(col(\"Quantity\") > 0,\n", "         ((col(\"Unit_Price\") - (col(\"Sales\") / col(\"Quantity\"))) / col(\"Unit_Price\") * 100))\n", "    .otherwise(0).alias(\"DiscountPercent\"),\n", "    \n", "    # Sales performance flags\n", "    when(col(\"Sales\") >= 2000, \"High-Performer\")\n", "    .when(col(\"Sales\") >= 500, \"Good-Performer\")\n", "    .otherwise(\"Standard\").alias(\"SalesPerformanceFlag\"),\n", "    \n", "    # Data quality checks\n", "    when(col(\"Sales\") <= 0, \"Invalid Sales Amount\")\n", "    .when(col(\"Quantity\") <= 0, \"Invalid Quantity\")\n", "    .when(col(\"Unit_Price\") <= 0, \"Invalid Unit Price\")\n", "    .otherwise(\"Valid\").alias(\"DataQualityFlag\"),\n", "    \n", "    # Metadata\n", "    current_timestamp().alias(\"ProcessedTimestamp\"),\n", "    lit(\"silver_transformation_v1\").alias(\"TransformationVersion\")\n", ")\n", "\n", "print(\"✅ Sales Silver transformations completed\")\n", "df_sales_silver.show(5, truncate=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Enhanced Reseller Transformations (Corrected Column Names)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read bronze reseller data\n", "df_reseller_bronze = spark.table(f\"{catalog}.{bronze_schema}.reseller\")\n", "\n", "# Enhanced Reseller transformations with CORRECT column names\n", "df_reseller_silver = df_reseller_bronze.select(\n", "    col(\"ResellerKey\"),\n", "    col(\"Business_Type\").alias(\"BusinessType\"),\n", "    col(\"Reseller\").alias(\"ResellerName\"),\n", "    col(\"City\"),\n", "    col(\"State_Province\").alias(\"StateProvince\"),\n", "    col(\"Country_Region\").alias(\"CountryRegion\"),\n", "    \n", "    # 🏪 BUSINESS INTELLIGENCE\n", "    \n", "    # Business size classification\n", "    when(col(\"Business_Type\").contains(\"Warehouse\"), \"Large\")\n", "    .when(col(\"Business_Type\").contains(\"Specialty\"), \"Medium\")\n", "    .when(col(\"Business_Type\").contains(\"Value\"), \"Small\")\n", "    .otherwise(\"Unknown\").alias(\"BusinessSize\"),\n", "    \n", "    # Channel type\n", "    when(col(\"Business_Type\").contains(\"Warehouse\"), \"Wholesale\")\n", "    .when(col(\"Business_Type\").contains(\"Specialty\"), \"Specialty Retail\")\n", "    .otherwise(\"General Retail\").alias(\"ChannelType\"),\n", "    \n", "    # Geographic tier (based on common business locations)\n", "    when(col(\"City\").isin([\"Seattle\", \"New York\", \"Los Angeles\", \"London\", \"Paris\"]), \"Tier 1\")\n", "    .when(col(\"State_Province\").isin([\"California\", \"New York\", \"Washington\"]), \"Tier 2\")\n", "    .otherwise(\"Tier 3\").alias(\"GeographicTier\"),\n", "    \n", "    # Country classification\n", "    when(col(\"Country_Region\").isin([\"United States\", \"Canada\"]), \"North America\")\n", "    .when(col(\"Country_Region\").isin([\"United Kingdom\", \"France\", \"Germany\"]), \"Europe\")\n", "    .when(col(\"Country_Region\") == \"Australia\", \"Oceania\")\n", "    .otherwise(\"Other\").alias(\"Continent\"),\n", "    \n", "    # Market maturity\n", "    when(col(\"Country_Region\").isin([\"United States\", \"United Kingdom\", \"Germany\"]), \"Mature\")\n", "    .when(col(\"Country_Region\").isin([\"France\", \"Canada\"]), \"Developing\")\n", "    .otherwise(\"Emerging\").alias(\"MarketMaturity\"),\n", "    \n", "    # Data quality\n", "    when(col(\"City\").isNull() | (col(\"City\") == \"\"), \"Missing City\")\n", "    .when(col(\"State_Province\").isNull(), \"Missing State\")\n", "    .otherwise(\"Valid\").alias(\"DataQualityFlag\"),\n", "    \n", "    # Metadata\n", "    current_timestamp().alias(\"ProcessedTimestamp\"),\n", "    lit(\"silver_transformation_v1\").alias(\"TransformationVersion\")\n", ")\n", "\n", "print(\"✅ Reseller Silver transformations completed\")\n", "df_reseller_silver.show(5, truncate=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Enhanced Salesperson Transformations (Corrected Column Names)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read bronze salesperson data\n", "df_salesperson_bronze = spark.table(f\"{catalog}.{bronze_schema}.salesperson\")\n", "\n", "# Enhanced Salesperson transformations with CORRECT column names\n", "df_salesperson_silver = df_salesperson_bronze.select(\n", "    col(\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\").alias(\"Salesperson<PERSON><PERSON>\"),  # Map for consistency\n", "    col(\"EmployeeID\"),\n", "    col(\"Salesperson\").alias(\"Salesperson<PERSON><PERSON>\"),\n", "    col(\"Title\"),\n", "    col(\"UPN\").alias(\"UserPrincipalName\"),\n", "    \n", "    # 👤 HR & PERFORMANCE ANALYTICS\n", "    \n", "    # Extract first and last name\n", "    split(col(\"Salesperson\"), \" \").getItem(0).alias(\"FirstName\"),\n", "    split(col(\"Salesperson\"), \" \").getItem(1).alias(\"LastName\"),\n", "    \n", "    # Seniority level from title\n", "    when(col(\"Title\").contains(\"Manager\"), \"Management\")\n", "    .when(col(\"Title\").contains(\"Senior\"), \"Senior\")\n", "    .when(col(\"Title\").contains(\"Representative\"), \"Representative\")\n", "    .otherwise(\"Other\").alias(\"SeniorityLevel\"),\n", "    \n", "    # Department classification\n", "    when(col(\"Title\").contains(\"Sales\"), \"Sales\")\n", "    .when(col(\"Title\").contains(\"Marketing\"), \"Marketing\")\n", "    .otherwise(\"Other\").alias(\"Department\"),\n", "    \n", "    # Email domain analysis\n", "    regexp_extract(col(\"UPN\"), \"@(.+)\", 1).alias(\"EmailDomain\"),\n", "    \n", "    # Username length (for system analysis)\n", "    length(regexp_extract(col(\"UPN\"), \"(.+)@\", 1)).alias(\"UsernameLength\"),\n", "    \n", "    # Employee ID analysis\n", "    length(col(\"EmployeeID\")).alias(\"EmployeeIDLength\"),\n", "    \n", "    # Data quality checks\n", "    when(col(\"UPN\").isNull() | (col(\"UPN\") == \"\"), \"Missing Email\")\n", "    .when(col(\"Title\").is<PERSON><PERSON>(), \"Missing Title\")\n", "    .otherwise(\"Valid\").alias(\"DataQualityFlag\"),\n", "    \n", "    # Metadata\n", "    current_timestamp().alias(\"ProcessedTimestamp\"),\n", "    lit(\"silver_transformation_v1\").alias(\"TransformationVersion\")\n", ")\n", "\n", "print(\"✅ Salesperson Silver transformations completed\")\n", "df_salesperson_silver.show(5, truncate=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Enhanced Targets Transformations (Corrected Column Names)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read bronze targets data\n", "df_targets_bronze = spark.table(f\"{catalog}.{bronze_schema}.targets\")\n", "\n", "# Enhanced Targets transformations with CORRECT column names\n", "# Actual schema: EmployeeID, Target, TargetMonth\n", "df_targets_silver = df_targets_bronze.select(\n", "    col(\"EmployeeID\"),\n", "    col(\"Target\"),\n", "    col(\"TargetMonth\"),\n", "    \n", "    # 🎯 TARGET ANALYSIS\n", "    \n", "    # Target categories\n", "    when(col(\"Target\") >= 1000000, \"High-Target\")\n", "    .when(col(\"Target\") >= 500000, \"Medium-Target\")\n", "    .when(col(\"Target\") >= 100000, \"Low-Target\")\n", "    .otherwise(\"Minimal-Target\").alias(\"TargetCategory\"),\n", "    \n", "    # Target tier for compensation planning\n", "    when(col(\"Target\") >= 1500000, \"Tier 1\")\n", "    .when(col(\"Target\") >= 750000, \"Tier 2\")\n", "    .when(col(\"Target\") >= 250000, \"Tier 3\")\n", "    .otherwise(\"Tier 4\").alias(\"TargetTier\"),\n", "    \n", "    # 📅 TEMPORAL TARGET ANALYSIS\n", "    \n", "    # Extract year and month from TargetMonth\n", "    year(col(\"Target<PERSON><PERSON><PERSON>\")).alias(\"TargetYear\"),\n", "    month(col(\"TargetMonth\")).alias(\"TargetMonthNumber\"),\n", "    quarter(col(\"TargetMonth\")).alias(\"TargetQuarter\"),\n", "    \n", "    # Month name for reporting\n", "    date_format(col(\"TargetMonth\"), \"MMMM\").alias(\"TargetMonthName\"),\n", "    \n", "    # Season classification\n", "    when(month(col(\"TargetMonth\")).isin([12, 1, 2]), \"Winter\")\n", "    .when(month(col(\"TargetMonth\")).isin([3, 4, 5]), \"Spring\")\n", "    .when(month(col(\"TargetMonth\")).isin([6, 7, 8]), \"Summer\")\n", "    .otherwise(\"Fall\").alias(\"TargetSeason\"),\n", "    \n", "    # 💰 TARGET BREAKDOWNS\n", "    \n", "    # Daily target (assuming 22 working days per month)\n", "    (col(\"Target\") / 22).alias(\"DailyTarget\"),\n", "    \n", "    # Weekly target (assuming 4.33 weeks per month)\n", "    (col(\"Target\") / 4.33).alias(\"WeeklyTarget\"),\n", "    \n", "    # Target per working day (more precise)\n", "    when(month(col(\"TargetMonth\")) == 2, col(\"Target\") / 20)  # February\n", "    .when(month(col(\"TargetMonth\")).isin([4, 6, 9, 11]), col(\"Target\") / 22)  # 30-day months\n", "    .otherwise(col(\"Target\") / 23).alias(\"PreciseDailyTarget\"),  # 31-day months\n", "    \n", "    # 🎯 PERFORMANCE INDICATORS\n", "    \n", "    # Target intensity (relative to month)\n", "    when(col(\"Target\") >= 500000, \"High-Intensity\")\n", "    .when(col(\"Target\") >= 200000, \"Medium-Intensity\")\n", "    .when(col(\"Target\") >= 50000, \"Low-Intensity\")\n", "    .otherwise(\"Minimal-Intensity\").alias(\"TargetIntensity\"),\n", "    \n", "    # Data quality checks\n", "    when(col(\"Target\") <= 0, \"Invalid Target\")\n", "    .when(col(\"TargetMonth\").is<PERSON><PERSON>(), \"Missing Target Month\")\n", "    .otherwise(\"Valid\").alias(\"DataQualityFlag\"),\n", "    \n", "    # Metadata\n", "    current_timestamp().alias(\"ProcessedTimestamp\"),\n", "    lit(\"silver_transformation_v1\").alias(\"TransformationVersion\")\n", ")\n", "\n", "print(\"✅ Targets Silver transformations completed\")\n", "print(\"\\nTargets sample:\")\n", "df_targets_silver.show(10, truncate=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}