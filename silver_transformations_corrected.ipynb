# Import required libraries
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql.window import Window
import re

# Catalog and schema configuration
catalog = "adventworks"
bronze_schema = "bronze_schema"
silver_schema = "silver_schema"

spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {bronze_schema}")

# Create silver schema if it doesn't exist
spark.sql(f"CREATE SCHEMA IF NOT EXISTS {catalog}.{silver_schema}")

print(f"Working with catalog: {catalog}")
print(f"Bronze schema: {bronze_schema}")
print(f"Silver schema: {silver_schema}")

# Read bronze product data
df_product_bronze = spark.table(f"{catalog}.{bronze_schema}.product")

# Enhanced Product transformations
df_product_silver = df_product_bronze.select(
    col("ProductKey"),
    col("Product").alias("ProductName"),
    col("Standard_Cost").alias("StandardCost"),
    col("Color"),
    col("Subcategory"),
    col("Category"),
    
    # 🔍 BUSINESS INTELLIGENCE TRANSFORMATIONS
    
    # Extract size from product name (for bikes, clothing, etc.)
    regexp_extract(col("Product"), r"(\d+)", 1).alias("ExtractedSize"),
    
    # Extract product line/series (HL, ML, LL)
    regexp_extract(col("Product"), r"^(HL|ML|LL)", 1).alias("ProductLine"),
    
    # Product tier based on naming convention
    when(col("Product").startswith("HL"), "High-End")
    .when(col("Product").startswith("ML"), "Mid-Range")
    .when(col("Product").startswith("LL"), "Entry-Level")
    .when(col("Product").contains("100"), "Premium")
    .when(col("Product").contains("200"), "Standard")
    .when(col("Product").contains("300"), "Basic")
    .otherwise("Unclassified").alias("ProductTier"),
    
    # Cost categories for pricing strategy
    when(col("Standard_Cost") >= 1000, "High-Cost")
    .when(col("Standard_Cost") >= 100, "Medium-Cost")
    .when(col("Standard_Cost") >= 10, "Low-Cost")
    .otherwise("Ultra-Low-Cost").alias("CostCategory"),
    
    # Gender targeting (for clothing/bikes)
    when(col("Product").contains("-W ") | col("Product").contains("Women"), "Women")
    .when(col("Product").contains("Men"), "Men")
    .otherwise("Unisex").alias("TargetGender"),
    
    # Seasonal products
    when(col("Subcategory").isin(["Gloves", "Tights", "Vests"]), "Winter")
    .when(col("Subcategory").isin(["Shorts", "Jerseys"]), "Summer")
    .otherwise("All-Season").alias("Seasonality"),
    
    # Product complexity (number of variants)
    when(col("Product").contains("Frame") | col("Product").contains("Bike"), "Complex")
    .when(col("Category") == "Components", "Moderate")
    .otherwise("Simple").alias("ProductComplexity"),
    
    # Color standardization and grouping
    when(col("Color").isin(["Black", "White", "Silver", "Grey"]), "Neutral")
    .when(col("Color").isin(["Red", "Blue", "Yellow"]), "Primary")
    .when(col("Color") == "Multi", "Multi-Color")
    .when(col("Color") == "NA", "No Color")
    .otherwise("Other").alias("ColorGroup"),
    
    # Existing color formatting (keeping your good work!)
    col("Background_Color_Format"),
    col("Font_Color_Format"),
    
    # Data quality flags
    when(col("Standard_Cost").isNull() | (col("Standard_Cost") <= 0), "Invalid Cost")
    .when(col("Color").isNull(), "Missing Color")
    .otherwise("Valid").alias("DataQualityFlag"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp"),
    lit("silver_transformation_v1").alias("TransformationVersion")
)

print("✅ Product Silver transformations completed")
df_product_silver.show(5, truncate=False)

# Read bronze sales data
df_sales_bronze = spark.table(f"{catalog}.{bronze_schema}.sales")

# Enhanced Sales transformations with CORRECT column names
df_sales_silver = df_sales_bronze.select(
    col("SalesOrderNumber"),
    col("OrderDate"),
    col("ProductKey"),
    col("ResellerKey"),
    col("EmployeeKey").alias("SalespersonKey"),  # Map EmployeeKey to SalespersonKey for consistency
    col("SalesTerritoryKey"),
    col("Quantity").alias("OrderQuantity"),
    col("Unit_Price").alias("UnitPrice"),
    col("Sales").alias("SalesAmount"),
    
    # 💰 REVENUE & PROFITABILITY ANALYSIS
    
    # Total order value (Sales column already includes total)
    col("Sales").alias("TotalOrderValue"),
    
    # Revenue per unit
    when(col("Quantity") > 0, col("Sales") / col("Quantity"))
    .otherwise(0).alias("RevenuePerUnit"),
    
    # Order size categories
    when(col("Quantity") >= 10, "Bulk")
    .when(col("Quantity") >= 5, "Medium")
    .when(col("Quantity") >= 2, "Small")
    .otherwise("Single").alias("OrderSizeCategory"),
    
    # Revenue tiers
    when(col("Sales") >= 5000, "High-Value")
    .when(col("Sales") >= 1000, "Medium-Value")
    .when(col("Sales") >= 100, "Low-Value")
    .otherwise("Minimal-Value").alias("RevenueCategory"),
    
    # 📅 TIME-BASED ANALYSIS
    
    # Extract date components
    year(col("OrderDate")).alias("OrderYear"),
    month(col("OrderDate")).alias("OrderMonth"),
    quarter(col("OrderDate")).alias("OrderQuarter"),
    dayofweek(col("OrderDate")).alias("OrderDayOfWeek"),
    weekofyear(col("OrderDate")).alias("OrderWeek"),
    
    # Business day classification
    when(dayofweek(col("OrderDate")).isin([1, 7]), "Weekend")
    .otherwise("Weekday").alias("DayType"),
    
    # Season classification
    when(month(col("OrderDate")).isin([12, 1, 2]), "Winter")
    .when(month(col("OrderDate")).isin([3, 4, 5]), "Spring")
    .when(month(col("OrderDate")).isin([6, 7, 8]), "Summer")
    .otherwise("Fall").alias("Season"),
    
    # Month name for reporting
    date_format(col("OrderDate"), "MMMM").alias("OrderMonthName"),
    
    # Days since epoch (for trend analysis)
    datediff(col("OrderDate"), lit("1900-01-01")).alias("DaysSinceEpoch"),
    
    # 🎯 BUSINESS METRICS
    
    # Discount calculation (if UnitPrice vs Sales/Quantity differs)
    when(col("Quantity") > 0,
         ((col("Unit_Price") - (col("Sales") / col("Quantity"))) / col("Unit_Price") * 100))
    .otherwise(0).alias("DiscountPercent"),
    
    # Sales performance flags
    when(col("Sales") >= 2000, "High-Performer")
    .when(col("Sales") >= 500, "Good-Performer")
    .otherwise("Standard").alias("SalesPerformanceFlag"),
    
    # Data quality checks
    when(col("Sales") <= 0, "Invalid Sales Amount")
    .when(col("Quantity") <= 0, "Invalid Quantity")
    .when(col("Unit_Price") <= 0, "Invalid Unit Price")
    .otherwise("Valid").alias("DataQualityFlag"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp"),
    lit("silver_transformation_v1").alias("TransformationVersion")
)

print("✅ Sales Silver transformations completed")
df_sales_silver.show(5, truncate=False)

# Read bronze reseller data
df_reseller_bronze = spark.table(f"{catalog}.{bronze_schema}.reseller")

# Enhanced Reseller transformations with CORRECT column names
df_reseller_silver = df_reseller_bronze.select(
    col("ResellerKey"),
    col("Business_Type").alias("BusinessType"),
    col("Reseller").alias("ResellerName"),
    col("City"),
    col("State_Province").alias("StateProvince"),
    col("Country_Region").alias("CountryRegion"),
    
    # 🏪 BUSINESS INTELLIGENCE
    
    # Business size classification
    when(col("Business_Type").contains("Warehouse"), "Large")
    .when(col("Business_Type").contains("Specialty"), "Medium")
    .when(col("Business_Type").contains("Value"), "Small")
    .otherwise("Unknown").alias("BusinessSize"),
    
    # Channel type
    when(col("Business_Type").contains("Warehouse"), "Wholesale")
    .when(col("Business_Type").contains("Specialty"), "Specialty Retail")
    .otherwise("General Retail").alias("ChannelType"),
    
    # Geographic tier (based on common business locations)
    when(col("City").isin(["Seattle", "New York", "Los Angeles", "London", "Paris"]), "Tier 1")
    .when(col("State_Province").isin(["California", "New York", "Washington"]), "Tier 2")
    .otherwise("Tier 3").alias("GeographicTier"),
    
    # Country classification
    when(col("Country_Region").isin(["United States", "Canada"]), "North America")
    .when(col("Country_Region").isin(["United Kingdom", "France", "Germany"]), "Europe")
    .when(col("Country_Region") == "Australia", "Oceania")
    .otherwise("Other").alias("Continent"),
    
    # Market maturity
    when(col("Country_Region").isin(["United States", "United Kingdom", "Germany"]), "Mature")
    .when(col("Country_Region").isin(["France", "Canada"]), "Developing")
    .otherwise("Emerging").alias("MarketMaturity"),
    
    # Data quality
    when(col("City").isNull() | (col("City") == ""), "Missing City")
    .when(col("State_Province").isNull(), "Missing State")
    .otherwise("Valid").alias("DataQualityFlag"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp"),
    lit("silver_transformation_v1").alias("TransformationVersion")
)

print("✅ Reseller Silver transformations completed")
df_reseller_silver.show(5, truncate=False)

# Read bronze salesperson data
df_salesperson_bronze = spark.table(f"{catalog}.{bronze_schema}.salesperson")

# Enhanced Salesperson transformations with CORRECT column names
df_salesperson_silver = df_salesperson_bronze.select(
    col("EmployeeKey").alias("SalespersonKey"),  # Map for consistency
    col("EmployeeID"),
    col("Salesperson").alias("SalespersonName"),
    col("Title"),
    col("UPN").alias("UserPrincipalName"),
    
    # 👤 HR & PERFORMANCE ANALYTICS
    
    # Extract first and last name
    split(col("Salesperson"), " ").getItem(0).alias("FirstName"),
    split(col("Salesperson"), " ").getItem(1).alias("LastName"),
    
    # Seniority level from title
    when(col("Title").contains("Manager"), "Management")
    .when(col("Title").contains("Senior"), "Senior")
    .when(col("Title").contains("Representative"), "Representative")
    .otherwise("Other").alias("SeniorityLevel"),
    
    # Department classification
    when(col("Title").contains("Sales"), "Sales")
    .when(col("Title").contains("Marketing"), "Marketing")
    .otherwise("Other").alias("Department"),
    
    # Email domain analysis
    regexp_extract(col("UPN"), "@(.+)", 1).alias("EmailDomain"),
    
    # Username length (for system analysis)
    length(regexp_extract(col("UPN"), "(.+)@", 1)).alias("UsernameLength"),
    
    # Employee ID analysis
    length(col("EmployeeID")).alias("EmployeeIDLength"),
    
    # Data quality checks
    when(col("UPN").isNull() | (col("UPN") == ""), "Missing Email")
    .when(col("Title").isNull(), "Missing Title")
    .otherwise("Valid").alias("DataQualityFlag"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp"),
    lit("silver_transformation_v1").alias("TransformationVersion")
)

print("✅ Salesperson Silver transformations completed")
df_salesperson_silver.show(5, truncate=False)