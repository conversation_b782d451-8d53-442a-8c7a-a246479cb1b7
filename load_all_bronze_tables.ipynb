{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Adventure Works Bronze Tables Loader\n", "\n", "This notebook loads all Adventure Works bronze tables from ADLS parquet files.\n", "\n", "**Tables to load:**\n", "- Product\n", "- Region\n", "- Reseller\n", "- Sales\n", "- Salesperson\n", "- SalespersonRegion\n", "- Targets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ADLS file path configuration\n", "filePath = r\"abfss://<EMAIL>/\"\n", "\n", "# List of bronze files with correct case-sensitive paths\n", "bronze_files = [\n", "    \"Product/Product.parquet\",\n", "    \"Region/Region.parquet\",\n", "    \"Reseller/Reseller.parquet\",\n", "    \"Sales/Sales.parquet\",\n", "    \"Salesperson/Salesperson.parquet\",\n", "    \"SalespersonRegion/SalespersonRegion.parquet\",\n", "    \"Targets/Targets.parquet\"\n", "]\n", "\n", "print(f\"Base file path: {filePath}\")\n", "print(f\"Bronze files to load: {len(bronze_files)} files\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Product Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read Product table\n", "df_product = (spark.read\n", "              .format('parquet')\n", "              .load(filePath + 'Product/Product.parquet')\n", ")\n", "print(\"PRODUCT TABLE:\")\n", "df_product.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Region Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read Region table\n", "df_region = (spark.read\n", "             .format('parquet')\n", "             .load(filePath + 'Region/Region.parquet')\n", ")\n", "print(\"REGION TABLE:\")\n", "df_region.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Reseller Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read Reseller table\n", "df_reseller = (spark.read\n", "               .format('parquet')\n", "               .load(filePath + 'Reseller/Reseller.parquet')\n", ")\n", "print(\"RESELLER TABLE:\")\n", "df_reseller.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Sales Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read Sales table\n", "df_sales = (spark.read\n", "            .format('parquet')\n", "            .load(filePath + 'Sales/Sales.parquet')\n", ")\n", "print(\"SALES TABLE:\")\n", "df_sales.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Salesperson Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read Salesperson table\n", "df_salesperson = (spark.read\n", "                  .format('parquet')\n", "                  .load(filePath + 'Salesperson/Salesperson.parquet')\n", ")\n", "print(\"SALESPERSON TABLE:\")\n", "df_salesperson.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load SalespersonRegion Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read SalespersonRegion table\n", "df_salespersonregion = (spark.read\n", "                        .format('parquet')\n", "                        .load(filePath + 'SalespersonRegion/SalespersonRegion.parquet')\n", ")\n", "print(\"SALESPERSON REGION TABLE:\")\n", "df_salespersonregion.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Targets Table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read Targets table\n", "df_targets = (spark.read\n", "              .format('parquet')\n", "              .load(filePath + 'Targets/Targets.parquet')\n", ")\n", "print(\"TARGETS TABLE:\")\n", "df_targets.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary and Row Counts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Print summary\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"ALL TABLES LOADED SUCCESSFULLY!\")\n", "print(\"=\"*50)\n", "\n", "print(f\"df_product: {df_product.count()} rows\")\n", "print(f\"df_region: {df_region.count()} rows\")\n", "print(f\"df_reseller: {df_reseller.count()} rows\")\n", "print(f\"df_sales: {df_sales.count()} rows\")\n", "print(f\"df_salesperson: {df_salesperson.count()} rows\")\n", "print(f\"df_salespersonregion: {df_salespersonregion.count()} rows\")\n", "print(f\"df_targets: {df_targets.count()} rows\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Temporary Views for SQL Queries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create temporary views for SQL queries\n", "df_product.createOrReplaceTempView(\"product\")\n", "df_region.createOrReplaceTempView(\"region\")\n", "df_reseller.createOrReplaceTempView(\"reseller\")\n", "df_sales.createOrReplaceTempView(\"sales\")\n", "df_salesperson.createOrReplaceTempView(\"salesperson\")\n", "df_salespersonregion.createOrReplaceTempView(\"salespersonregion\")\n", "df_targets.createOrReplaceTempView(\"targets\")\n", "\n", "print(\"Temporary views created for SQL queries:\")\n", "print(\"- product\")\n", "print(\"- region\")\n", "print(\"- reseller\")\n", "print(\"- sales\")\n", "print(\"- salesperson\")\n", "print(\"- salespersonregion\")\n", "print(\"- targets\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example SQL Queries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example SQL queries\n", "print(\"Example: Sales count\")\n", "spark.sql('SELECT COUNT(*) as total_sales FROM sales').show()\n", "\n", "print(\"\\nExample: First 5 products\")\n", "spark.sql('SELECT * FROM product LIMIT 5').show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}