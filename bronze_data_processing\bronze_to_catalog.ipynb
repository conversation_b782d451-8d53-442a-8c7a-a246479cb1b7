{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Adventure Works Bronze to Catalog Migration\n", "\n", "This notebook reads parquet files from ADLS and provides advanced features:\n", "\n", "**Features:**\n", "- Data profiling and quality checks\n", "- Schema analysis\n", "- Optional catalog writing\n", "- Comprehensive reporting\n", "- Error handling and validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import *\n", "from pyspark.sql.types import *"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ADLS file path configuration\n", "filePath = r\"abfss://<EMAIL>/\"\n", "bronze_path = filePath + \"bronze/\"\n", "\n", "# Catalog and schema configuration\n", "catalog = \"adventure_works\"\n", "schema = \"bronze_schema\"\n", "\n", "print(f\"Base path: {filePath}\")\n", "print(f\"Bronze path: {bronze_path}\")\n", "print(f\"Target catalog: {catalog}.{schema}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# List of bronze files with correct case-sensitive paths\n", "bronze_files = [\n", "    \"Product/Product.parquet\",\n", "    \"Region/Region.parquet\",\n", "    \"Reseller/Reseller.parquet\",\n", "    \"Sales/Sales.parquet\",\n", "    \"Salesperson/Salesperson.parquet\",\n", "    \"SalespersonRegion/SalespersonRegion.parquet\",\n", "    \"Targets/Targets.parquet\"\n", "]\n", "\n", "# Extract table names for processing (lowercase for consistency)\n", "bronze_tables = [file_path.split('/')[0].lower() for file_path in bronze_files]\n", "\n", "print(f\"Bronze files: {bronze_files}\")\n", "print(f\"Table names: {bronze_tables}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define Helper Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_bronze_table(file_path, table_name):\n", "    \"\"\"Read a single bronze table from ADLS using the correct file path\"\"\"\n", "    try:\n", "        full_path = bronze_path + file_path\n", "        print(f\"Reading from: {full_path}\")\n", "        \n", "        df = (spark.read\n", "              .format('parquet')\n", "              .load(full_path)\n", "        )\n", "        return df\n", "    except Exception as e:\n", "        print(f\"Error reading {table_name} from {file_path}: {str(e)}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def profile_dataframe(df, table_name):\n", "    \"\"\"Generate basic profiling information for a DataFrame\"\"\"\n", "    print(f\"\\n📊 PROFILING: {table_name.upper()}\")\n", "    print(\"=\" * 40)\n", "    \n", "    # Basic stats\n", "    row_count = df.count()\n", "    col_count = len(df.columns)\n", "    \n", "    print(f\"Rows: {row_count:,}\")\n", "    print(f\"Columns: {col_count}\")\n", "    print(f\"Schema:\")\n", "    df.printSchema()\n", "    \n", "    # Column statistics\n", "    print(f\"\\nColumn Details:\")\n", "    for col_name in df.columns:\n", "        col_type = dict(df.dtypes)[col_name]\n", "        null_count = df.filter(col(col_name).isNull()).count()\n", "        null_pct = (null_count / row_count * 100) if row_count > 0 else 0\n", "        \n", "        print(f\"  {col_name} ({col_type}): {null_count:,} nulls ({null_pct:.1f}%)\")\n", "    \n", "    return {\n", "        'table_name': table_name,\n", "        'row_count': row_count,\n", "        'column_count': col_count,\n", "        'columns': df.columns\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def write_to_catalog(df, table_name, catalog, schema, mode=\"overwrite\"):\n", "    \"\"\"Write DataFrame to the catalog as a Delta table\"\"\"\n", "    try:\n", "        full_table_name = f\"{catalog}.{schema}.{table_name}\"\n", "        \n", "        (df.write\n", "         .format(\"delta\")\n", "         .mode(mode)\n", "         .option(\"mergeSchema\", \"true\")\n", "         .saveAsTable(full_table_name)\n", "        )\n", "        \n", "        print(f\"✓ Successfully wrote {table_name} to {full_table_name}\")\n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"✗ Error writing {table_name} to catalog: {str(e)}\")\n", "        return False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main Processing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Main execution\n", "print(\"🚀 Adventure Works Bronze Layer Processing\")\n", "print(\"=\" * 50)\n", "\n", "# Dictionary to store results\n", "results = {}\n", "dataframes = {}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Process each table with correct file paths\n", "for i, table_name in enumerate(bronze_tables):\n", "    file_path = bronze_files[i]\n", "    print(f\"\\n📂 Processing {table_name} from {file_path}...\")\n", "    \n", "    # Read the table\n", "    df = read_bronze_table(file_path, table_name)\n", "    \n", "    if df is not None:\n", "        # Store DataFrame\n", "        dataframes[f\"df_{table_name}\"] = df\n", "        \n", "        # Profile the data\n", "        profile_info = profile_dataframe(df, table_name)\n", "        results[table_name] = profile_info\n", "        \n", "        # Show sample data\n", "        print(f\"\\n📋 Sample data from {table_name}:\")\n", "        df.show(3, truncate=False)\n", "        \n", "        # Create temporary view\n", "        df.createOrReplaceTempView(f\"bronze_{table_name}\")\n", "        print(f\"✓ Created temporary view: bronze_{table_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary Report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary report\n", "print(\"\\n📈 SUMMARY REPORT\")\n", "print(\"=\" * 50)\n", "rows_list = [info['row_count'] for info in results.values()]\n", "total_rows = sum(rows_list)\n", "print(f\"Total tables processed: {len(results)}\")\n", "print(f\"Total rows across all tables: {total_rows:,}\")\n", "\n", "print(f\"\\nTable Summary:\")\n", "for table_name, info in results.items():\n", "    print(f\"  {table_name}: {info['row_count']:,} rows, {info['column_count']} columns\")\n", "\n", "print(f\"\\nAvailable DataFrames: {list(dataframes.keys())}\")\n", "print(f\"Available Views: {['bronze_' + table for table in bronze_tables]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Optional: Write to Catalog\n", "\n", "Uncomment the cell below to write all tables to the catalog as Delta tables."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Optional: Write to catalog (uncomment to execute)\n", "# print(f\"\\n💾 Writing to catalog {catalog}.{schema}...\")\n", "# for table_name in bronze_tables:\n", "#     if f\"df_{table_name}\" in dataframes:\n", "#         write_to_catalog(dataframes[f\"df_{table_name}\"], table_name, catalog, schema)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example Queries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Product analysis\n", "if 'df_product' in dataframes:\n", "    print(\"Product DataFrame analysis:\")\n", "    dataframes['df_product'].describe().show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Sales summary via SQL\n", "try:\n", "    print(\"Sales summary:\")\n", "    spark.sql(\"\"\"\n", "        SELECT \n", "            COUNT(*) as total_records,\n", "            COUNT(DISTINCT SalesOrderNumber) as unique_orders\n", "        FROM bronze_sales\n", "    \"\"\").show()\n", "except:\n", "    print(\"Sales table not available for analysis\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}