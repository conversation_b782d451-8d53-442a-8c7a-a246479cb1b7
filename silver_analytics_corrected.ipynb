{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Adventure Works Silver Layer - Advanced Analytics (Corrected)\n", "\n", "This notebook creates advanced analytical tables using the **correct column names** from your Adventure Works data.\n", "\n", "## Corrected Column Mappings:\n", "- **Sales**: `Employee<PERSON><PERSON>` → `Salesperson<PERSON>ey`, `Quantity` → `OrderQuantity`, `Sales` → `SalesAmount`\n", "- **Reseller**: `Business_Type` → `BusinessType`, `State_Province` → `StateProvince`\n", "- **Salesperson**: `Employee<PERSON><PERSON>` → `Salesperson<PERSON>ey`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql.functions import *\n", "from pyspark.sql.types import *\n", "from pyspark.sql.window import Window"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration\n", "catalog = \"adventworks\"\n", "bronze_schema = \"bronze_schema\"\n", "silver_schema = \"silver_schema\"\n", "\n", "spark.sql(f\"USE CATALOG {catalog}\")\n", "spark.sql(f\"USE SCHEMA {bronze_schema}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Customer Analytics & Lifetime Value (Corrected)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read sales data for customer analysis\n", "df_sales = spark.table(f\"{catalog}.{bronze_schema}.sales\")\n", "\n", "# Note: Since there's no Customer<PERSON>ey in the actual schema, we'll use ResellerKey as customer proxy\n", "# Customer Lifetime Value and Behavior Analysis using ResellerKey\n", "customer_window = Window.partitionBy(\"ResellerKey\")\n", "customer_order_window = Window.partitionBy(\"ResellerKey\").orderBy(\"OrderDate\")\n", "\n", "df_customer_analytics = df_sales.select(\n", "    col(\"ResellerKey\").alias(\"CustomerKey\"),  # Using ResellerKey as customer identifier\n", "    col(\"OrderDate\"),\n", "    col(\"Sales\").alias(\"SalesAmount\"),\n", "    col(\"Quantity\").alias(\"OrderQuantity\"),\n", "    col(\"ProductKey\")\n", ").groupBy(\"CustomerKey\").agg(\n", "    # 💰 FINANCIAL METRICS\n", "    sum(\"SalesAmount\").alias(\"TotalLifetimeValue\"),\n", "    avg(\"SalesAmount\").alias(\"AverageOrderValue\"),\n", "    max(\"SalesAmount\").alias(\"LargestOrderValue\"),\n", "    min(\"SalesAmount\").alias(\"SmallestOrderValue\"),\n", "    \n", "    # 📊 BEHAVIORAL METRICS\n", "    count(\"*\").alias(\"TotalOrders\"),\n", "    sum(\"OrderQuantity\").alias(\"TotalItemsPurchased\"),\n", "    avg(\"OrderQuantity\").alias(\"AverageItemsPerOrder\"),\n", "    countDistinct(\"ProductKey\").alias(\"UniqueProductsPurchased\"),\n", "    \n", "    # 📅 TEMPORAL METRICS\n", "    min(\"OrderDate\").alias(\"FirstOrderDate\"),\n", "    max(\"OrderDate\").alias(\"LastOrderDate\"),\n", "    datediff(max(\"OrderDate\"), min(\"OrderDate\")).alias(\"CustomerLifespanDays\")\n", ").withColumn(\n", "    # 🎯 CUSTOMER SEGMENTATION\n", "    \"CustomerValueSegment\",\n", "    when(col(\"TotalLifetimeValue\") >= 50000, \"VIP\")\n", "    .when(col(\"TotalLifetimeValue\") >= 20000, \"High-Value\")\n", "    .when(col(\"TotalLifetimeValue\") >= 5000, \"Medium-Value\")\n", "    .otherwise(\"Low-Value\")\n", ").withColumn(\n", "    \"CustomerFrequencySegment\",\n", "    when(col(\"TotalOrders\") >= 20, \"Frequent\")\n", "    .when(col(\"TotalOrders\") >= 10, \"Regular\")\n", "    .when(col(\"TotalOrders\") >= 5, \"Occasional\")\n", "    .otherwise(\"Infrequent\")\n", ").withColumn(\n", "    \"CustomerRecencySegment\",\n", "    when(datediff(current_date(), col(\"LastOrderDate\")) <= 30, \"Recent\")\n", "    .when(datediff(current_date(), col(\"LastOrderDate\")) <= 90, \"Active\")\n", "    .when(datediff(current_date(), col(\"LastOrderDate\")) <= 365, \"Dormant\")\n", "    .otherwise(\"Inactive\")\n", ").withColumn(\n", "    # Calculate order frequency (orders per year)\n", "    \"OrderFrequencyPerYear\",\n", "    when(col(\"CustomerLifespanDays\") > 0, \n", "         col(\"TotalOrders\") * 365.0 / col(\"CustomerLifespanDays\"))\n", "    .otherwise(col(\"TotalOrders\"))\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ Customer Analytics completed (using ResellerKey as customer proxy)\")\n", "df_customer_analytics.show(5, truncate=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Product Performance Analytics (Corrected)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Product Performance with Rankings\n", "df_product = spark.table(f\"{catalog}.{bronze_schema}.product\")\n", "\n", "# Join sales with product data for comprehensive analysis\n", "df_product_performance = df_sales.join(df_product, \"ProductKey\").groupBy(\n", "    \"ProductKey\", \"Product\", \"Category\", \"Subcategory\", \"Standard_Cost\"\n", ").agg(\n", "    # 📈 SALES PERFORMANCE (using correct column names)\n", "    sum(\"Sales\").alias(\"TotalRevenue\"),\n", "    sum(\"Quantity\").alias(\"TotalUnitsSold\"),\n", "    count(\"*\").alias(\"TotalOrders\"),\n", "    countDistinct(\"ResellerKey\").alias(\"UniqueCustomers\"),  # Using ResellerKey\n", "    avg(\"Sales\").alias(\"AverageOrderValue\"),\n", "    avg(\"Quantity\").alias(\"AverageQuantityPerOrder\")\n", ").withColumn(\n", "    # 💰 PROFITABILITY METRICS\n", "    \"EstimatedProfit\", \n", "    col(\"TotalRevenue\") - (col(\"TotalUnitsSold\") * col(\"Standard_Cost\"))\n", ").withColumn(\n", "    \"ProfitMarginPercent\",\n", "    when(col(\"TotalRevenue\") > 0, \n", "         (col(\"EstimatedProfit\") / col(\"TotalRevenue\") * 100))\n", "    .otherwise(0)\n", ").withColumn(\n", "    \"RevenuePerCustomer\",\n", "    when(col(\"UniqueCustomers\") > 0, col(\"TotalRevenue\") / col(\"UniqueCustomers\"))\n", "    .otherwise(0)\n", ")\n", "\n", "# Add rankings\n", "revenue_window = Window.orderBy(desc(\"TotalRevenue\"))\n", "units_window = Window.orderBy(desc(\"TotalUnitsSold\"))\n", "profit_window = Window.orderBy(desc(\"EstimatedProfit\"))\n", "\n", "df_product_performance = df_product_performance.withColumn(\n", "    \"RevenueRank\", row_number().over(revenue_window)\n", ").withColumn(\n", "    \"UnitsRank\", row_number().over(units_window)\n", ").withColumn(\n", "    \"ProfitRank\", row_number().over(profit_window)\n", ").withColumn(\n", "    # 🏆 PERFORMANCE CATEGORIES\n", "    \"RevenuePerformance\",\n", "    when(col(\"RevenueRank\") <= 10, \"Top 10\")\n", "    .when(col(\"RevenueRank\") <= 50, \"Top 50\")\n", "    .when(col(\"RevenueRank\") <= 100, \"Top 100\")\n", "    .otherwise(\"Others\")\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ Product Performance Analytics completed\")\n", "df_product_performance.orderBy(desc(\"TotalRevenue\")).show(5, truncate=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Sales Trend Analysis (Corrected)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Daily Sales Trends with Moving Averages (using correct column names)\n", "df_daily_sales = df_sales.groupBy(\"OrderDate\").agg(\n", "    sum(\"Sales\").alias(\"DailySales\"),  # Using 'Sales' column\n", "    sum(\"Quantity\").alias(\"DailyUnits\"),  # Using 'Quantity' column\n", "    count(\"*\").alias(\"DailyOrders\"),\n", "    countDistinct(\"ResellerKey\").alias(\"DailyUniqueCustomers\")  # Using ResellerKey\n", ").orderBy(\"OrderDate\")\n", "\n", "# Define windows for moving averages\n", "days_7_window = Window.orderBy(\"OrderDate\").rowsBetween(-6, 0)\n", "days_30_window = Window.orderBy(\"OrderDate\").rowsBetween(-29, 0)\n", "days_90_window = Window.orderBy(\"OrderDate\").rowsBetween(-89, 0)\n", "\n", "df_sales_trends = df_daily_sales.withColumn(\n", "    # 📊 MOVING AVERAGES\n", "    \"Sales_7DayMA\", avg(\"DailySales\").over(days_7_window)\n", ").withColumn(\n", "    \"Sales_30DayMA\", avg(\"DailySales\").over(days_30_window)\n", ").withColumn(\n", "    \"Sales_90DayMA\", avg(\"DailySales\").over(days_90_window)\n", ").withColumn(\n", "    \"Orders_7DayMA\", avg(\"DailyOrders\").over(days_7_window)\n", ").withColumn(\n", "    \"Orders_30DayMA\", avg(\"DailyOrders\").over(days_30_window)\n", ").withColumn(\n", "    # 📈 GROWTH CALCULATIONS\n", "    \"SalesGrowth_7Day\",\n", "    when(lag(\"Sales_7DayMA\", 7).over(Window.orderBy(\"OrderDate\")) > 0,\n", "         ((col(\"Sales_7DayMA\") - lag(\"Sales_7DayMA\", 7).over(Window.orderBy(\"OrderDate\"))) / \n", "          lag(\"Sales_7DayMA\", 7).over(Window.orderBy(\"OrderDate\")) * 100))\n", "    .otherwise(0)\n", ").withColumn(\n", "    \"SalesGrowth_30Day\",\n", "    when(lag(\"Sales_30DayMA\", 30).over(Window.orderBy(\"OrderDate\")) > 0,\n", "         ((col(\"Sales_30DayMA\") - lag(\"Sales_30DayMA\", 30).over(Window.orderBy(\"OrderDate\"))) / \n", "          lag(\"Sales_30DayMA\", 30).over(Window.orderBy(\"OrderDate\")) * 100))\n", "    .otherwise(0)\n", ").withColumn(\n", "    # 🎯 TREND INDICATORS\n", "    \"TrendDirection\",\n", "    when(col(\"SalesGrowth_7Day\") > 5, \"Strong Growth\")\n", "    .when(col(\"SalesGrowth_7Day\") > 0, \"Growth\")\n", "    .when(col(\"SalesGrowth_7Day\") > -5, \"Stable\")\n", "    .otherwise(\"Decline\")\n", ").withColumn(\n", "    # Add date components for analysis\n", "    \"Year\", year(\"OrderDate\")\n", ").withColumn(\n", "    \"Month\", month(\"OrderDate\")\n", ").withColumn(\n", "    \"Quarter\", quarter(\"OrderDate\")\n", ").withColumn(\n", "    \"DayOfWeek\", dayofweek(\"OrderDate\")\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ Sales Trend Analysis completed\")\n", "df_sales_trends.orderBy(desc(\"OrderDate\")).show(5, truncate=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Territory Performance Analysis (Corrected)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Territory Performance Analysis (using correct column names)\n", "df_region = spark.table(f\"{catalog}.{bronze_schema}.region\")\n", "df_salesperson = spark.table(f\"{catalog}.{bronze_schema}.salesperson\")\n", "df_targets = spark.table(f\"{catalog}.{bronze_schema}.targets\")\n", "\n", "# Join sales with territory and salesperson information\n", "df_territory_sales = df_sales.join(\n", "    df_region, \"SalesTerritoryKey\"\n", ").join(\n", "    df_salesperson, df_sales.Employee<PERSON>ey == df_salesperson.Employee<PERSON><PERSON>, \"left\"\n", ").join(\n", "    df_targets, df_salesperson.EmployeeKey == df_targets.Salesperson<PERSON><PERSON>, \"left\"\n", ")\n", "\n", "# Territory performance metrics\n", "df_territory_performance = df_territory_sales.groupBy(\n", "    \"SalesTerritoryKey\", \"Region\", \"Country\", \"Group\"\n", ").agg(\n", "    # 💰 FINANCIAL PERFORMANCE (using correct column names)\n", "    sum(\"Sales\").alias(\"TotalRevenue\"),  # Using 'Sales' column\n", "    avg(\"Sales\").alias(\"AverageOrderValue\"),\n", "    sum(\"Quantity\").alias(\"TotalUnits\"),  # Using 'Quantity' column\n", "    count(\"*\").alias(\"TotalOrders\"),\n", "    countDistinct(\"ResellerKey\").alias(\"UniqueCustomers\"),  # Using ResellerKey\n", "    countDistinct(df_salesperson.Employee<PERSON>ey).alias(\"ActiveSalespeople\"),\n", "    sum(\"TargetBase\").alias(\"TotalTargets\")\n", ").withColumn(\n", "    # 📊 PERFORMANCE RATIOS\n", "    \"RevenuePerSalesperson\",\n", "    when(col(\"ActiveSalespeople\") > 0, col(\"TotalRevenue\") / col(\"ActiveSalespeople\"))\n", "    .otherwise(0)\n", ").withColumn(\n", "    \"RevenuePerCustomer\",\n", "    when(col(\"UniqueCustomers\") > 0, col(\"TotalRevenue\") / col(\"UniqueCustomers\"))\n", "    .otherwise(0)\n", ").withColumn(\n", "    \"TargetAchievementPercent\",\n", "    when(col(\"TotalTargets\") > 0, (col(\"TotalRevenue\") / col(\"TotalTargets\") * 100))\n", "    .otherwise(0)\n", ").withColumn(\n", "    # 🏆 PERFORMANCE RANKINGS\n", "    \"RevenueRank\",\n", "    row_number().over(Window.orderBy(desc(\"TotalRevenue\")))\n", ").withColumn(\n", "    \"TargetAchievementRank\",\n", "    row_number().over(Window.orderBy(desc(\"TargetAchievementPercent\")))\n", ").withColumn(\n", "    # 🎯 PERFORMANCE CATEGORIES\n", "    \"PerformanceCategory\",\n", "    when(col(\"TargetAchievementPercent\") >= 120, \"Exceeds Expectations\")\n", "    .when(col(\"TargetAchievementPercent\") >= 100, \"Meets Expectations\")\n", "    .when(col(\"TargetAchievementPercent\") >= 80, \"Below Expectations\")\n", "    .otherwise(\"Needs Improvement\")\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ Territory Performance Analysis completed\")\n", "df_territory_performance.orderBy(desc(\"TotalRevenue\")).show(5, truncate=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}