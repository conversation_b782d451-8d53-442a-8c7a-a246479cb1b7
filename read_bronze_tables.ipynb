{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Adventure Works Bronze Layer Data Reader\n", "\n", "This notebook reads all parquet files from the bronze folder in ADLS using a loop-based approach with error handling.\n", "\n", "**Features:**\n", "- Loop-based reading for efficiency\n", "- Error handling for missing files\n", "- Data profiling and summary\n", "- Automatic temporary view creation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import *"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ADLS file path configuration\n", "filePath = r\"abfss://<EMAIL>/\"\n", "bronze_path = filePath + \"bronze/\"\n", "\n", "# Catalog and schema configuration\n", "catalog = \"adventure_works\"\n", "schema = \"bronze_schema\"\n", "\n", "print(f\"Base path: {filePath}\")\n", "print(f\"Bronze path: {bronze_path}\")\n", "print(f\"Target catalog: {catalog}.{schema}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# List of all tables in bronze layer\n", "bronze_tables = [\n", "    \"product\",\n", "    \"region\", \n", "    \"reseller\",\n", "    \"sales\",\n", "    \"salesperson\",\n", "    \"salespersonregion\",\n", "    \"targets\"\n", "]\n", "\n", "print(f\"Tables to process: {bronze_tables}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Dictionary to store all DataFrames\n", "dataframes = {}\n", "\n", "print(\"Reading Adventure Works Bronze Layer Tables...\")\n", "print(\"=\" * 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read all parquet files from bronze folder\n", "for table_name in bronze_tables:\n", "    try:\n", "        print(f\"Reading {table_name} table...\")\n", "        \n", "        # Read parquet file\n", "        df = (spark.read\n", "              .format('parquet')\n", "              .load(bronze_path + table_name)\n", "        )\n", "        \n", "        # Store DataFrame in dictionary\n", "        dataframes[f\"df_{table_name}\"] = df\n", "        \n", "        # Display basic info about the table\n", "        print(f\"✓ {table_name.upper()} - Rows: {df.count()}, Columns: {len(df.columns)}\")\n", "        print(f\"  Columns: {', '.join(df.columns)}\")\n", "        \n", "        # Show first few rows\n", "        print(f\"  Sample data:\")\n", "        df.show(5, truncate=False)\n", "        print(\"-\" * 50)\n", "        \n", "    except Exception as e:\n", "        print(f\"✗ Error reading {table_name}: {str(e)}\")\n", "        print(\"-\" * 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"All bronze tables loaded successfully!\")\n", "print(\"\\nAvailable DataFrames:\")\n", "for df_name in dataframes.keys():\n", "    print(f\"- {df_name}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create temporary views for SQL queries\n", "print(\"Creating temporary views...\")\n", "for table_name in bronze_tables:\n", "    df_name = f\"df_{table_name}\"\n", "    if df_name in dataframes:\n", "        dataframes[df_name].createOrReplaceTempView(f\"bronze_{table_name}\")\n", "        print(f\"✓ Created view: bronze_{table_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Usage Examples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"You can now use these DataFrames:\")\n", "print(\"Example usage:\")\n", "print(\"- dataframes['df_product'].show()\")\n", "print(\"- spark.sql('SELECT * FROM bronze_sales LIMIT 10').show()\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Show product data\n", "if 'df_product' in dataframes:\n", "    print(\"Product DataFrame example:\")\n", "    dataframes['df_product'].show(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: SQL query on sales\n", "try:\n", "    print(\"Sales count via SQL:\")\n", "    spark.sql('SELECT COUNT(*) as total_sales FROM bronze_sales').show()\n", "except:\n", "    print(\"Sales table not available for SQL query\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}