# Import required libraries
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql.window import Window
import re

# Catalog and schema configuration
catalog = "adventworks"
bronze_schema = "bronze_schema"
silver_schema = "silver_schema"

spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {bronze_schema}")

# Create silver schema if it doesn't exist
spark.sql(f"CREATE SCHEMA IF NOT EXISTS {catalog}.{silver_schema}")

print(f"Working with catalog: {catalog}")
print(f"Bronze schema: {bronze_schema}")
print(f"Silver schema: {silver_schema}")

# Read bronze product data
df_product_bronze = spark.table(f"{catalog}.{bronze_schema}.product")

# Enhanced Product transformations
df_product_silver = df_product_bronze.select(
    col("ProductKey"),
    col("Product").alias("ProductName"),
    col("Standard_Cost").alias("StandardCost"),
    col("Color"),
    col("Subcategory"),
    col("Category"),
    
    # 🔍 BUSINESS INTELLIGENCE TRANSFORMATIONS
    
    # Extract size from product name (for bikes, clothing, etc.)
    regexp_extract(col("Product"), r"(\d+)", 1).alias("ExtractedSize"),
    
    # Extract product line/series (HL, ML, LL)
    regexp_extract(col("Product"), r"^(HL|ML|LL)", 1).alias("ProductLine"),
    
    # Product tier based on naming convention
    when(col("Product").startswith("HL"), "High-End")
    .when(col("Product").startswith("ML"), "Mid-Range")
    .when(col("Product").startswith("LL"), "Entry-Level")
    .when(col("Product").contains("100"), "Premium")
    .when(col("Product").contains("200"), "Standard")
    .when(col("Product").contains("300"), "Basic")
    .otherwise("Unclassified").alias("ProductTier"),
    
    # Cost categories for pricing strategy
    when(col("Standard_Cost") >= 1000, "High-Cost")
    .when(col("Standard_Cost") >= 100, "Medium-Cost")
    .when(col("Standard_Cost") >= 10, "Low-Cost")
    .otherwise("Ultra-Low-Cost").alias("CostCategory"),
    
    # Gender targeting (for clothing/bikes)
    when(col("Product").contains("-W ") | col("Product").contains("Women"), "Women")
    .when(col("Product").contains("Men"), "Men")
    .otherwise("Unisex").alias("TargetGender"),
    
    # Seasonal products
    when(col("Subcategory").isin(["Gloves", "Tights", "Vests"]), "Winter")
    .when(col("Subcategory").isin(["Shorts", "Jerseys"]), "Summer")
    .otherwise("All-Season").alias("Seasonality"),
    
    # Product complexity (number of variants)
    when(col("Product").contains("Frame") | col("Product").contains("Bike"), "Complex")
    .when(col("Category") == "Components", "Moderate")
    .otherwise("Simple").alias("ProductComplexity"),
    
    # Color standardization and grouping
    when(col("Color").isin(["Black", "White", "Silver", "Grey"]), "Neutral")
    .when(col("Color").isin(["Red", "Blue", "Yellow"]), "Primary")
    .when(col("Color") == "Multi", "Multi-Color")
    .when(col("Color") == "NA", "No Color")
    .otherwise("Other").alias("ColorGroup"),
    
    # Existing color formatting (keeping your good work!)
    col("Background_Color_Format"),
    col("Font_Color_Format"),
    
    # Data quality flags
    when(col("Standard_Cost").isNull() | (col("Standard_Cost") <= 0), "Invalid Cost")
    .when(col("Color").isNull(), "Missing Color")
    .otherwise("Valid").alias("DataQualityFlag"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp"),
    lit("silver_transformation_v1").alias("TransformationVersion")
)

print("✅ Product Silver transformations completed")
df_product_silver.display(10)

df_sales_bronze.columns

# Read bronze sales data
df_sales_bronze = spark.table(f"{catalog}.{bronze_schema}.sales")

# Enhanced Sales transformations with CORRECT column names
df_sales_silver = df_sales_bronze.select(
    col("SalesOrderNumber"),
    col("OrderDate"),
    col("ProductKey"),
    col("ResellerKey"),
    col("EmployeeKey").alias("SalespersonKey"),  # Map EmployeeKey to SalespersonKey for consistency
    col("SalesTerritoryKey"),
    col("Quantity").alias("OrderQuantity"),
    col("Unit_Price").alias("UnitPrice"),
    col("Sales").alias("SalesAmount"),
    
    # 💰 REVENUE & PROFITABILITY ANALYSIS
    
    # Total order value (Sales column already includes total)
    col("Sales").alias("TotalOrderValue"),
    
    # Revenue per unit
    when(col("Quantity") > 0, col("Sales") / col("Quantity"))
    .otherwise(0).alias("RevenuePerUnit"),
    
    # Order size categories
    when(col("Quantity") >= 10, "Bulk")
    .when(col("Quantity") >= 5, "Medium")
    .when(col("Quantity") >= 2, "Small")
    .otherwise("Single").alias("OrderSizeCategory"),
    
    # Revenue tiers
    when(col("Sales") >= 5000, "High-Value")
    .when(col("Sales") >= 1000, "Medium-Value")
    .when(col("Sales") >= 100, "Low-Value")
    .otherwise("Minimal-Value").alias("RevenueCategory"),
    
    # 📅 TIME-BASED ANALYSIS
    
    # Extract date components
    year(col("OrderDate")).alias("OrderYear"),
    month(col("OrderDate")).alias("OrderMonth"),
    quarter(col("OrderDate")).alias("OrderQuarter"),
    dayofweek(col("OrderDate")).alias("OrderDayOfWeek"),
    weekofyear(col("OrderDate")).alias("OrderWeek"),
    
    # Business day classification
    when(dayofweek(col("OrderDate")).isin([1, 7]), "Weekend")
    .otherwise("Weekday").alias("DayType"),
    
    # Season classification
    when(month(col("OrderDate")).isin([12, 1, 2]), "Winter")
    .when(month(col("OrderDate")).isin([3, 4, 5]), "Spring")
    .when(month(col("OrderDate")).isin([6, 7, 8]), "Summer")
    .otherwise("Fall").alias("Season"),
    
    # Month name for reporting
    date_format(col("OrderDate"), "MMMM").alias("OrderMonthName"),
    
    # Days since epoch (for trend analysis)
    datediff(col("OrderDate"), lit("1900-01-01")).alias("DaysSinceEpoch"),
    
    # 🎯 BUSINESS METRICS
    
    # Discount calculation (if UnitPrice vs Sales/Quantity differs)
    when(col("Quantity") > 0,
         ((col("Unit_Price") - (col("Sales") / col("Quantity"))) / col("Unit_Price") * 100))
    .otherwise(0).alias("DiscountPercent"),
    
    # Sales performance flags
    when(col("Sales") >= 2000, "High-Performer")
    .when(col("Sales") >= 500, "Good-Performer")
    .otherwise("Standard").alias("SalesPerformanceFlag"),
    
    # Data quality checks
    when(col("Sales") <= 0, "Invalid Sales Amount")
    .when(col("Quantity") <= 0, "Invalid Quantity")
    .when(col("Unit_Price") <= 0, "Invalid Unit Price")
    .otherwise("Valid").alias("DataQualityFlag"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp"),
    lit("silver_transformation_v1").alias("TransformationVersion")
)

print("✅ Sales Silver transformations completed")
df_sales_silver.display(10)

df_reseller_bronze.columns

# Read bronze region and reseller data
df_region_bronze = spark.table(f"{catalog}.{bronze_schema}.region")

# Enhanced Region transformations
df_region_silver = df_region_bronze.select(
    col("SalesTerritoryKey"),
    col("Region"),
    col("Country"),
    col("Group").alias("RegionGroup"),
    
    # 🌍 GEOGRAPHIC INTELLIGENCE
    
    # Continent classification
    when(col("Country").isin(["United States", "Canada"]), "North America")
    .when(col("Country").isin(["United Kingdom", "France", "Germany"]), "Europe")
    .when(col("Country") == "Australia", "Oceania")
    .otherwise("Other").alias("Continent"),
    
    # Market maturity
    when(col("Country").isin(["United States", "United Kingdom", "Germany"]), "Mature")
    .when(col("Country").isin(["France", "Canada"]), "Developing")
    .otherwise("Emerging").alias("MarketMaturity"),
    
    # Language group (for marketing)
    when(col("Country").isin(["United States", "United Kingdom", "Canada", "Australia"]), "English")
    .when(col("Country") == "France", "French")
    .when(col("Country") == "Germany", "German")
    .otherwise("Other").alias("PrimaryLanguage"),
    
    # Time zone groups (for operational planning)
    when(col("Country").isin(["United States", "Canada"]), "Americas")
    .when(col("Country").isin(["United Kingdom", "France", "Germany"]), "EMEA")
    .when(col("Country") == "Australia", "APAC")
    .otherwise("Other").alias("TimeZoneGroup"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp")
)

print("✅ Region and Reseller Silver transformations completed")
print("Region sample:")
df_region_silver.display(10)

# Read bronze reseller data
df_reseller_bronze = spark.table(f"{catalog}.{bronze_schema}.reseller")

# Enhanced Reseller transformations with CORRECT column names
df_reseller_silver = df_reseller_bronze.select(
    col("ResellerKey"),
    col("Business_Type").alias("BusinessType"),
    col("Reseller").alias("ResellerName"),
    col("City"),
    col("State_Province").alias("StateProvince"),
    col("Country_Region").alias("CountryRegion"),
    
    # 🏪 BUSINESS INTELLIGENCE
    
    # Business size classification
    when(col("Business_Type").contains("Warehouse"), "Large")
    .when(col("Business_Type").contains("Specialty"), "Medium")
    .when(col("Business_Type").contains("Value"), "Small")
    .otherwise("Unknown").alias("BusinessSize"),
    
    # Channel type
    when(col("Business_Type").contains("Warehouse"), "Wholesale")
    .when(col("Business_Type").contains("Specialty"), "Specialty Retail")
    .otherwise("General Retail").alias("ChannelType"),
    
    # Geographic tier (based on common business locations)
    when(col("City").isin(["Seattle", "New York", "Los Angeles", "London", "Paris"]), "Tier 1")
    .when(col("State_Province").isin(["California", "New York", "Washington"]), "Tier 2")
    .otherwise("Tier 3").alias("GeographicTier"),
    
    # Country classification
    when(col("Country_Region").isin(["United States", "Canada"]), "North America")
    .when(col("Country_Region").isin(["United Kingdom", "France", "Germany"]), "Europe")
    .when(col("Country_Region") == "Australia", "Oceania")
    .otherwise("Other").alias("Continent"),
    
    # Market maturity
    when(col("Country_Region").isin(["United States", "United Kingdom", "Germany"]), "Mature")
    .when(col("Country_Region").isin(["France", "Canada"]), "Developing")
    .otherwise("Emerging").alias("MarketMaturity"),
    
    # Data quality
    when(col("City").isNull() | (col("City") == ""), "Missing City")
    .when(col("State_Province").isNull(), "Missing State")
    .otherwise("Valid").alias("DataQualityFlag"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp"),
    lit("silver_transformation_v1").alias("TransformationVersion")
)

print("✅ Reseller Silver transformations completed")
df_reseller_silver.display(10)

df_targets_bronze.columns

df_targets_bronze = spark.table(f"{catalog}.{bronze_schema}.targets")

# ✅ CORRECTED TARGETS SCHEMA
df_targets_silver = df_targets_bronze.select(
    col("EmployeeID"),           # ✅ Not SalespersonKey
    col("Target"),               # ✅ Not TargetBase  
    col("TargetMonth"),          # ✅ NEW temporal dimension

    # Target categories
    when(col("Target") >= 1000000, "High-Target")
    .when(col("Target") >= 500000, "Medium-Target")
    .when(col("Target") >= 100000, "Low-Target")
    .otherwise("Minimal-Target").alias("TargetCategory"),
    
    # 📅 TEMPORAL ANALYSIS
    year(col("TargetMonth")).alias("TargetYear"),
    month(col("TargetMonth")).alias("TargetMonthNumber"),
    quarter(col("TargetMonth")).alias("TargetQuarter"),
    
    # 💰 PRECISE BREAKDOWNS
    (col("Target") / 22).alias("DailyTarget"),
    when(month(col("TargetMonth")) == 2, col("Target") / 20)
    .when(month(col("TargetMonth")).isin([4, 6, 9, 11]), col("Target") / 22)
    .otherwise(col("Target") / 23).alias("PreciseDailyTarget")
)

print("✅ Targets Silver transformations completed")
print("\nTargets sample:")
df_targets_silver.display(10)

# Read bronze salesperson data
df_salesperson_bronze = spark.table(f"{catalog}.{bronze_schema}.salesperson")

# Enhanced Salesperson transformations with CORRECT column names
df_salesperson_silver = df_salesperson_bronze.select(
    col("EmployeeKey").alias("SalespersonKey"),  # Map for consistency
    col("EmployeeID"),
    col("Salesperson").alias("SalespersonName"),
    col("Title"),
    col("UPN").alias("UserPrincipalName"),
    
    # 👤 HR & PERFORMANCE ANALYTICS
    
    # Extract first and last name
    split(col("Salesperson"), " ").getItem(0).alias("FirstName"),
    split(col("Salesperson"), " ").getItem(1).alias("LastName"),
    
    # Seniority level from title
    when(col("Title").contains("Manager"), "Management")
    .when(col("Title").contains("Senior"), "Senior")
    .when(col("Title").contains("Representative"), "Representative")
    .otherwise("Other").alias("SeniorityLevel"),
    
    # Department classification
    when(col("Title").contains("Sales"), "Sales")
    .when(col("Title").contains("Marketing"), "Marketing")
    .otherwise("Other").alias("Department"),
    
    # Email domain analysis
    regexp_extract(col("UPN"), "@(.+)", 1).alias("EmailDomain"),
    
    # Username length (for system analysis)
    length(regexp_extract(col("UPN"), "(.+)@", 1)).alias("UsernameLength"),
    
    # Employee ID analysis
    length(col("EmployeeID")).alias("EmployeeIDLength"),
    
    # Data quality checks
    when(col("UPN").isNull() | (col("UPN") == ""), "Missing Email")
    .when(col("Title").isNull(), "Missing Title")
    .otherwise("Valid").alias("DataQualityFlag"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp"),
    lit("silver_transformation_v1").alias("TransformationVersion")
)

print("✅ Salesperson Silver transformations completed")
df_salesperson_silver.display(10)

def write_to_silver(df, table_name, catalog, schema, mode="overwrite"):
    """Write DataFrame to the catalog as a Delta table"""
    try:
        full_table_name = f"{catalog}.{schema}.{table_name}"
        
        (df.write
         .format("delta")
         .mode(mode)
         .option("mergeSchema", "true")
         .saveAsTable(full_table_name)
        )
        
        print(f"✓ Successfully wrote {table_name} to {full_table_name}")
        return True
        
    except Exception as e:
        print(f"✗ Error writing {table_name} to catalog: {str(e)}")
        return False


# Map silver table names to their corresponding DataFrame variables
# FIXED: Pass actual DataFrames, not string variable names
silver_mapping = {
    "product": df_product_silver,
    "sales": df_sales_silver,
    "reseller": df_reseller_silver,
    "salesperson": df_salesperson_silver,
    "targets": df_targets_silver
}

# Write to silver 
print(f"\n Writing to catalog {catalog}.{silver_schema}...")
for table_name, df_var in silver_mapping.items():
    write_to_silver(df_var, table_name, catalog, silver_schema)

# Gold layer configuration
gold_schema = "gold_schema"
spark.sql(f"CREATE SCHEMA IF NOT EXISTS {catalog}.{gold_schema}")

print(f"✅ Gold schema {catalog}.{gold_schema} ready")
print(f"📊 Reading from Silver: {catalog}.{silver_schema}")
print(f"🥇 Writing to Gold: {catalog}.{gold_schema}")

# Read from Silver layer tables
df_sales_silver = spark.table(f"{catalog}.{silver_schema}.sales")
df_product_silver = spark.table(f"{catalog}.{silver_schema}.product")
df_reseller_silver = spark.table(f"{catalog}.{silver_schema}.reseller")
df_salesperson_silver = spark.table(f"{catalog}.{silver_schema}.salesperson")
df_targets_silver = spark.table(f"{catalog}.{silver_schema}.targets")

print("✅ Silver layer tables loaded")

# Executive KPI Dashboard - Monthly aggregated metrics
df_executive_kpis = df_sales_silver.join(df_product_silver, "ProductKey") \
    .select(
        col("OrderYear").alias("Year"),
        col("OrderMonth").alias("Month"),
        col("Category"),
        col("Subcategory"),
        col("SalesAmount").alias("Revenue"),
        col("OrderQuantity").alias("Quantity"),
        col("StandardCost"),
        col("ResellerKey"),
        col("EmployeeKey")
    ).groupBy("Year", "Month", "Category", "Subcategory").agg(
        # 💰 FINANCIAL KPIs
        sum("Revenue").alias("TotalRevenue"),
        sum("Quantity").alias("TotalUnitsSold"),
        sum(col("Quantity") * col("StandardCost")).alias("TotalCOGS"),
        count("*").alias("TotalOrders"),
        countDistinct("ResellerKey").alias("ActiveCustomers"),
        countDistinct("EmployeeKey").alias("ActiveSalespeople"),
        
        # 📊 BUSINESS METRICS
        avg("Revenue").alias("AvgOrderValue"),
        avg("Quantity").alias("AvgOrderSize")
    ).withColumn(
        # 💡 CALCULATED KPIs
        "GrossProfit", col("TotalRevenue") - col("TotalCOGS")
    ).withColumn(
        "GrossProfitMargin", 
        when(col("TotalRevenue") > 0, col("GrossProfit") / col("TotalRevenue") * 100)
        .otherwise(0)
    ).withColumn(
        "RevenuePerCustomer",
        when(col("ActiveCustomers") > 0, col("TotalRevenue") / col("ActiveCustomers"))
        .otherwise(0)
    ).withColumn(
        "RevenuePerSalesperson",
        when(col("ActiveSalespeople") > 0, col("TotalRevenue") / col("ActiveSalespeople"))
        .otherwise(0)
    ).withColumn(
        # 📅 TIME DIMENSIONS
        "YearMonth", concat(col("Year"), lit("-"), lpad(col("Month"), 2, "0"))
    ).withColumn(
        "Quarter", 
        when(col("Month").isin([1,2,3]), "Q1")
        .when(col("Month").isin([4,5,6]), "Q2")
        .when(col("Month").isin([7,8,9]), "Q3")
        .otherwise("Q4")
    ).withColumn(
        "ProcessedTimestamp", current_timestamp()
    )

print("✅ Executive KPI Dashboard created")
print(f"Records: {df_executive_kpis.count():,}")
df_executive_kpis.orderBy(desc("Year"), desc("Month"), desc("TotalRevenue")).show(5)

# Customer RFM Analysis (using ResellerKey as customer proxy)
analysis_date = df_sales_silver.agg(max("OrderDate")).collect()[0][0]

df_customer_rfm = df_sales_silver.groupBy("ResellerKey").agg(
    # 📅 RECENCY (days since last purchase)
    datediff(lit(analysis_date), max("OrderDate")).alias("Recency"),
    
    # 🔄 FREQUENCY (number of purchases)
    count("*").alias("Frequency"),
    
    # 💰 MONETARY (total spent)
    sum("SalesAmount").alias("Monetary"),
    
    # 📊 ADDITIONAL METRICS
    avg("SalesAmount").alias("AvgOrderValue"),
    countDistinct("ProductKey").alias("ProductDiversity"),
    min("OrderDate").alias("FirstPurchaseDate"),
    max("OrderDate").alias("LastPurchaseDate")
).withColumn(
    "CustomerLifespanDays",
    datediff(col("LastPurchaseDate"), col("FirstPurchaseDate"))
)

# Calculate RFM Scores (1-5 scale)
recency_quantiles = df_customer_rfm.approxQuantile("Recency", [0.2, 0.4, 0.6, 0.8], 0.01)
frequency_quantiles = df_customer_rfm.approxQuantile("Frequency", [0.2, 0.4, 0.6, 0.8], 0.01)
monetary_quantiles = df_customer_rfm.approxQuantile("Monetary", [0.2, 0.4, 0.6, 0.8], 0.01)

df_customer_segments = df_customer_rfm.withColumn(
    "R_Score",
    when(col("Recency") <= recency_quantiles[0], 5)
    .when(col("Recency") <= recency_quantiles[1], 4)
    .when(col("Recency") <= recency_quantiles[2], 3)
    .when(col("Recency") <= recency_quantiles[3], 2)
    .otherwise(1)
).withColumn(
    "F_Score",
    when(col("Frequency") >= frequency_quantiles[3], 5)
    .when(col("Frequency") >= frequency_quantiles[2], 4)
    .when(col("Frequency") >= frequency_quantiles[1], 3)
    .when(col("Frequency") >= frequency_quantiles[0], 2)
    .otherwise(1)
).withColumn(
    "M_Score",
    when(col("Monetary") >= monetary_quantiles[3], 5)
    .when(col("Monetary") >= monetary_quantiles[2], 4)
    .when(col("Monetary") >= monetary_quantiles[1], 3)
    .when(col("Monetary") >= monetary_quantiles[0], 2)
    .otherwise(1)
).withColumn(
    "RFM_Score", concat(col("R_Score"), col("F_Score"), col("M_Score"))
).withColumn(
    # 🎯 BUSINESS SEGMENTS
    "CustomerSegment",
    when((col("R_Score") >= 4) & (col("F_Score") >= 4) & (col("M_Score") >= 4), "Champions")
    .when((col("R_Score") >= 3) & (col("F_Score") >= 3) & (col("M_Score") >= 4), "Loyal Customers")
    .when((col("R_Score") >= 4) & (col("F_Score") <= 2), "New Customers")
    .when((col("R_Score") >= 3) & (col("F_Score") >= 3) & (col("M_Score") <= 3), "Potential Loyalists")
    .when((col("R_Score") <= 2) & (col("F_Score") >= 3) & (col("M_Score") >= 3), "At Risk")
    .when((col("R_Score") <= 2) & (col("F_Score") <= 2) & (col("M_Score") >= 4), "Can't Lose Them")
    .when((col("R_Score") <= 2) & (col("F_Score") <= 2) & (col("M_Score") <= 2), "Lost Customers")
    .otherwise("Others")
).withColumn(
    "ProcessedTimestamp", current_timestamp()
)

print("✅ Customer RFM Analysis completed")
print(f"Total customers: {df_customer_segments.count():,}")
print("\nCustomer Segments Distribution:")
df_customer_segments.groupBy("CustomerSegment").agg(
    count("*").alias("CustomerCount"),
    avg("Monetary").alias("AvgMonetaryValue")
).orderBy(desc("CustomerCount")).show()

# Product ABC Analysis for Inventory Management
df_product_performance = df_sales_silver.join(df_product_silver, "ProductKey").groupBy(
    "ProductKey", "ProductName", "Category", "Subcategory", "StandardCost"
).agg(
    sum("SalesAmount").alias("TotalRevenue"),
    sum("OrderQuantity").alias("TotalQuantitySold"),
    count("*").alias("TotalOrders"),
    countDistinct("ResellerKey").alias("UniqueCustomers"),
    avg("SalesAmount").alias("AvgOrderValue"),
    stddev("SalesAmount").alias("RevenueStdDev")
).withColumn(
    "TotalCost", col("TotalQuantitySold") * col("StandardCost")
).withColumn(
    "GrossProfit", col("TotalRevenue") - col("TotalCost")
).withColumn(
    "ProfitMargin",
    when(col("TotalRevenue") > 0, col("GrossProfit") / col("TotalRevenue") * 100)
    .otherwise(0)
)

# Calculate cumulative revenue percentage for ABC classification
total_revenue = df_product_performance.agg(sum("TotalRevenue")).collect()[0][0]

window_spec = Window.orderBy(desc("TotalRevenue"))
df_abc_analysis = df_product_performance.withColumn(
    "RevenueRank", row_number().over(window_spec)
).withColumn(
    "CumulativeRevenue", sum("TotalRevenue").over(window_spec.rowsBetween(Window.unboundedPreceding, 0))
).withColumn(
    "CumulativeRevenuePercent", col("CumulativeRevenue") / total_revenue * 100
).withColumn(
    # 📊 ABC CLASSIFICATION
    "ABC_Category",
    when(col("CumulativeRevenuePercent") <= 80, "A - High Value")
    .when(col("CumulativeRevenuePercent") <= 95, "B - Medium Value")
    .otherwise("C - Low Value")
).withColumn(
    # 🎯 BUSINESS RECOMMENDATIONS
    "InventoryStrategy",
    when(col("ABC_Category") == "A - High Value", "High Stock - Frequent Review")
    .when(col("ABC_Category") == "B - Medium Value", "Moderate Stock - Regular Review")
    .otherwise("Low Stock - Periodic Review")
).withColumn(
    "MarketingPriority",
    when((col("ABC_Category") == "A - High Value") & (col("ProfitMargin") >= 30), "High Priority")
    .when(col("ABC_Category") == "A - High Value", "Medium Priority")
    .otherwise("Low Priority")
).withColumn(
    "ProcessedTimestamp", current_timestamp()
)

print("✅ Product ABC Analysis completed")
print(f"Total products: {df_abc_analysis.count():,}")
print("\nABC Distribution:")
df_abc_analysis.groupBy("ABC_Category").agg(
    count("*").alias("ProductCount"),
    sum("TotalRevenue").alias("CategoryRevenue"),
    avg("ProfitMargin").alias("AvgProfitMargin")
).show()

# Sales Performance Scorecard with Target Achievement
df_sales_performance = df_sales_silver.join(
    df_salesperson_silver, "EmployeeKey"
).join(
    df_targets_silver, "EmployeeID", "left"
).select(
    col("EmployeeID"),
    col("SalespersonName"),
    col("JobTitle"),
    col("OrderYear").alias("Year"),
    col("OrderMonth").alias("Month"),
    col("SalesAmount").alias("Revenue"),
    col("TargetAmount"),
    col("TargetYear"),
    col("TargetMonthNumber")
).filter(
    # Align sales month with target month
    (col("Year") == col("TargetYear")) &
    (col("Month") == col("TargetMonthNumber"))
).groupBy(
    "EmployeeID", "SalespersonName", "JobTitle", "Year", "Month"
).agg(
    sum("Revenue").alias("ActualSales"),
    first("TargetAmount").alias("MonthlyTarget"),
    count("*").alias("OrdersCount")
).withColumn(
    # 🎯 PERFORMANCE METRICS
    "TargetAchievement",
    when(col("MonthlyTarget") > 0, col("ActualSales") / col("MonthlyTarget") * 100)
    .otherwise(0)
).withColumn(
    "SalesVariance", col("ActualSales") - col("MonthlyTarget")
).withColumn(
    "PerformanceRating",
    when(col("TargetAchievement") >= 120, "Exceeds")
    .when(col("TargetAchievement") >= 100, "Meets")
    .when(col("TargetAchievement") >= 80, "Below")
    .when(col("TargetAchievement") >= 50, "Poor")
    .otherwise("Critical")
).withColumn(
    # 🏆 RANKINGS
    "SalesRank",
    row_number().over(Window.partitionBy("Year", "Month").orderBy(desc("ActualSales")))
).withColumn(
    "AchievementRank",
    row_number().over(Window.partitionBy("Year", "Month").orderBy(desc("TargetAchievement")))
).withColumn(
    "ProcessedTimestamp", current_timestamp()
)

print("✅ Sales Performance Scorecard created")
print(f"Performance records: {df_sales_performance.count():,}")
print("\nPerformance Distribution:")
df_sales_performance.groupBy("PerformanceRating").agg(
    count("*").alias("Count"),
    avg("TargetAchievement").alias("AvgAchievement")
).orderBy(desc("Count")).show()

# Time Series Dataset for Data Scientists and ML Engineers
df_time_series = df_sales_silver.join(df_product_silver, "ProductKey") \
    .select(
        col("OrderDate"),
        col("SalesAmount").alias("Revenue"),
        col("OrderQuantity").alias("Quantity"),
        col("Category"),
        col("Subcategory")
    ).groupBy("OrderDate", "Category", "Subcategory").agg(
        sum("Revenue").alias("DailyRevenue"),
        sum("Quantity").alias("DailyQuantity"),
        count("*").alias("DailyOrders")
    )

# Add time-based features for ML
df_ml_features = df_time_series.withColumn(
    "Year", year(col("OrderDate"))
).withColumn(
    "Month", month(col("OrderDate"))
).withColumn(
    "DayOfYear", dayofyear(col("OrderDate"))
).withColumn(
    "DayOfWeek", dayofweek(col("OrderDate"))
).withColumn(
    "Quarter", quarter(col("OrderDate"))
).withColumn(
    "IsWeekend", when(dayofweek(col("OrderDate")).isin([1, 7]), 1).otherwise(0)
).withColumn(
    "IsMonthEnd", when(col("OrderDate") == last_day(col("OrderDate")), 1).otherwise(0)
)

# Add moving averages and lag features
window_7d = Window.partitionBy("Category", "Subcategory").orderBy("OrderDate").rowsBetween(-6, 0)
window_30d = Window.partitionBy("Category", "Subcategory").orderBy("OrderDate").rowsBetween(-29, 0)
window_lag = Window.partitionBy("Category", "Subcategory").orderBy("OrderDate")

df_ml_ready = df_ml_features.withColumn(
    "Revenue_7DayMA", avg("DailyRevenue").over(window_7d)
).withColumn(
    "Revenue_30DayMA", avg("DailyRevenue").over(window_30d)
).withColumn(
    "Revenue_Lag1", lag("DailyRevenue", 1).over(window_lag)
).withColumn(
    "Revenue_Lag7", lag("DailyRevenue", 7).over(window_lag)
).withColumn(
    # Growth rates
    "Revenue_Growth_1D",
    when(col("Revenue_Lag1") > 0, 
         (col("DailyRevenue") - col("Revenue_Lag1")) / col("Revenue_Lag1") * 100)
    .otherwise(0)
).withColumn(
    "Revenue_Growth_7D",
    when(col("Revenue_Lag7") > 0,
         (col("DailyRevenue") - col("Revenue_Lag7")) / col("Revenue_Lag7") * 100)
    .otherwise(0)
).withColumn(
    "ProcessedTimestamp", current_timestamp()
)

print("✅ ML-Ready Time Series Dataset created")
print(f"Total records: {df_ml_ready.count():,}")
date_range = df_ml_ready.agg(min('OrderDate'), max('OrderDate')).collect()[0]
print(f"Date range: {date_range[0]} to {date_range[1]}")
print(f"Categories: {df_ml_ready.select('Category').distinct().count()}")
print(f"Subcategories: {df_ml_ready.select('Subcategory').distinct().count()}")

def write_to_gold(df, table_name, catalog, schema, partition_cols=None, mode="overwrite"):
    """Write DataFrame to the Gold catalog as a Delta table"""
    try:
        full_table_name = f"{catalog}.{schema}.{table_name}"
        
        writer = (df.write
                 .format("delta")
                 .mode(mode)
                 .option("mergeSchema", "true")
                )
        
        if partition_cols:
            writer = writer.partitionBy(*partition_cols)
            print(f"   📁 Partitioned by: {partition_cols}")
        
        writer.saveAsTable(full_table_name)
        
        # Optimize table
        spark.sql(f"OPTIMIZE {full_table_name}")
        spark.sql(f"ANALYZE TABLE {full_table_name} COMPUTE STATISTICS")
        
        print(f"✓ Successfully wrote {table_name} to {full_table_name}")
        return True
        
    except Exception as e:
        print(f"✗ Error writing {table_name} to catalog: {str(e)}")
        return False

# Gold layer tables mapping
gold_tables = [
    (df_executive_kpis, "executive_kpis", ["Year", "Month"]),
    (df_customer_segments, "customer_rfm_segments", None),
    (df_abc_analysis, "product_abc_analysis", ["ABC_Category"]),
    (df_sales_performance, "sales_performance_scorecard", ["Year", "Month"]),
    (df_ml_ready, "ml_time_series_features", ["Year", "Month"])
]

print(f"\n🥇 Writing Gold layer tables to {catalog}.{gold_schema}...")
success_count = 0

for df, table_name, partition_cols in gold_tables:
    print(f"\n💾 Processing {table_name}...")
    if write_to_gold(df, table_name, catalog, gold_schema, partition_cols):
        success_count += 1

print(f"\n🎉 Gold Layer Complete!")
print(f"✅ Successfully created {success_count}/{len(gold_tables)} Gold tables")
print(f"\n📊 Available for:")
print(f"   • Business Intelligence Dashboards")
print(f"   • Executive Reporting & KPIs")
print(f"   • Machine Learning Models")
print(f"   • Advanced Analytics")
print(f"   • Data Science Analysis")