# Load all Adventure Works Bronze Tables from ADLS
# Following the same pattern as your product table example

# ADLS file path configuration
filePath = r"abfss://<EMAIL>/"

# List of bronze files with correct case-sensitive paths
bronze_files = [
    "Product/Product.parquet",
    "Region/Region.parquet",
    "Reseller/Reseller.parquet",
    "Sales/Sales.parquet",
    "Salesperson/Salesperson.parquet",
    "SalespersonRegion/SalespersonRegion.parquet",
    "Targets/Targets.parquet"
]

# Read Product table (corrected path)
df_product = (spark.read
              .format('parquet')
              .load(filePath + 'Product/Product.parquet')
)
print("PRODUCT TABLE:")
df_product.show()

# Read Region table
df_region = (spark.read
             .format('parquet')
             .load(filePath + 'Region/Region.parquet')
)
print("\nREGION TABLE:")
df_region.show()

# Read Reseller table
df_reseller = (spark.read
               .format('parquet')
               .load(filePath + 'Reseller/Reseller.parquet')
)
print("\nRESELLER TABLE:")
df_reseller.show()

# Read Sales table
df_sales = (spark.read
            .format('parquet')
            .load(filePath + 'Sales/Sales.parquet')
)
print("\nSALES TABLE:")
df_sales.show()

# Read Salesperson table
df_salesperson = (spark.read
                  .format('parquet')
                  .load(filePath + 'Salesperson/Salesperson.parquet')
)
print("\nSALESPERSON TABLE:")
df_salesperson.show()

# Read SalespersonRegion table
df_salespersonregion = (spark.read
                        .format('parquet')
                        .load(filePath + 'SalespersonRegion/SalespersonRegion.parquet')
)
print("\nSALESPERSON REGION TABLE:")
df_salespersonregion.show()

# Read Targets table
df_targets = (spark.read
              .format('parquet')
              .load(filePath + 'Targets/Targets.parquet')
)
print("\nTARGETS TABLE:")
df_targets.show()

# Print summary
print("\n" + "="*50)
print("ALL TABLES LOADED SUCCESSFULLY!")
print("="*50)

print(f"df_product: {df_product.count()} rows")
print(f"df_region: {df_region.count()} rows")
print(f"df_reseller: {df_reseller.count()} rows")
print(f"df_sales: {df_sales.count()} rows")
print(f"df_salesperson: {df_salesperson.count()} rows")
print(f"df_salespersonregion: {df_salespersonregion.count()} rows")
print(f"df_targets: {df_targets.count()} rows")

# Optional: Create temporary views for SQL queries
df_product.createOrReplaceTempView("product")
df_region.createOrReplaceTempView("region")
df_reseller.createOrReplaceTempView("reseller")
df_sales.createOrReplaceTempView("sales")
df_salesperson.createOrReplaceTempView("salesperson")
df_salespersonregion.createOrReplaceTempView("salespersonregion")
df_targets.createOrReplaceTempView("targets")

print("\nTemporary views created for SQL queries:")
print("- product")
print("- region")
print("- reseller")
print("- sales")
print("- salesperson")
print("- salespersonregion")
print("- targets")

# Example SQL query
print("\nExample: You can now run SQL queries like:")
print("spark.sql('SELECT COUNT(*) FROM sales').show()")
print("spark.sql('SELECT * FROM product LIMIT 5').show()")
