# Configuration
catalog = "adventworks"
silver_schema = "silver_schema"

# Load Silver layer tables
df_sales = spark.table(f"{catalog}.{silver_schema}.sales")
df_product = spark.table(f"{catalog}.{silver_schema}.product")
df_reseller = spark.table(f"{catalog}.{silver_schema}.reseller")
df_salesperson = spark.table(f"{catalog}.{silver_schema}.salesperson")

print("✅ Tables loaded")

# Check column names
print("SALES COLUMNS:")
print(df_sales.columns)
print("\nPRODUCT COLUMNS:")
print(df_product.columns)
print("\nRESELLER COLUMNS:")
print(df_reseller.columns)
print("\nSALESPERSON COLUMNS:")
print(df_salesperson.columns)

# Test simple join with correct column names
from pyspark.sql.functions import *

df_test = df_sales.join(df_product, "ProductKey") \
    .join(df_reseller, "ResellerKey") \
    .join(df_salesperson, "SalespersonKey") \
    .select(
        # Sales Information
        col("SalesOrderNumber"),
        col("OrderDate"),
        col("SalesAmount"),
        col("OrderQuantity"),
        
        # Product Information
        col("ProductName"),
        col("Category"),
        col("Subcategory"),
        
        # Customer Information - TESTING CORRECT NAMES
        col("ResellerName").alias("CustomerName"),
        col("BusinessType"),
        col("City"),
        col("StateProvince").alias("State"),
        col("CountryRegion").alias("Country"),
        
        # Salesperson Information
        col("SalespersonName"),
        col("Title").alias("JobTitle")
    )

print("✅ Test join successful!")
print(f"Records: {df_test.count():,}")
df_test.show(3)