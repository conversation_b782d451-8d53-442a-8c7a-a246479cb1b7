# Simple imports
from pyspark.sql.functions import *
from pyspark.sql.types import *

# Configuration
catalog = "adventworks"
silver_schema = "silver_schema"
gold_schema = "gold_schema"

# Create Gold schema
spark.sql(f"CREATE SCHEMA IF NOT EXISTS {catalog}.{gold_schema}")
print(f"✅ Gold schema {catalog}.{gold_schema} ready")

# Load Silver layer tables
df_sales = spark.table(f"{catalog}.{silver_schema}.sales")
df_product = spark.table(f"{catalog}.{silver_schema}.product")
df_reseller = spark.table(f"{catalog}.{silver_schema}.reseller")
df_salesperson = spark.table(f"{catalog}.{silver_schema}.salesperson")
df_targets = spark.table(f"{catalog}.{silver_schema}.targets")

print("✅ Silver layer tables loaded")
print(f"Sales records: {df_sales.count():,}")

# Create simple sales fact table - denormalized for Power BI
df_sales_fact = df_sales.join(df_product, "ProductKey") \
    .join(df_reseller, "ResellerKey") \
    .join(df_salesperson, "SalespersonKey") \
    .select(
        # Sales Information
        col("SalesOrderNumber"),
        col("OrderDate"),
        col("SalesAmount"),
        col("OrderQuantity"),
        col("UnitPrice"),
        
        # Product Information
        col("ProductKey"),
        col("ProductName"),
        col("Category"),
        col("Subcategory"),
        col("StandardCost"),
        col("Color"),
        
        # Customer Information
        col("ResellerKey"),
        col("Reseller").alias("CustomerName"),
        col("Business_Type").alias("BusinessType"),
        col("City"),
        col("State_Province").alias("State"),
        col("Country_Region").alias("Country"),
        
        # Salesperson Information
        col("SalespersonKey"),
        col("SalespersonName"),
        col("Title").alias("JobTitle"),
        
        # Date Dimensions (from Silver layer)
        col("OrderYear"),
        col("OrderMonth"),
        col("OrderQuarter"),
        col("OrderMonthName")
    ).withColumn(
        # Simple calculated columns for Power BI
        "GrossProfit", col("SalesAmount") - (col("OrderQuantity") * col("StandardCost"))
    ).withColumn(
        "ProfitMargin", 
        when(col("SalesAmount") > 0, col("GrossProfit") / col("SalesAmount") * 100).otherwise(0)
    )

print("✅ Sales Fact Table created")
print(f"Records: {df_sales_fact.count():,}")
df_sales_fact.show(3)

# Simple monthly summary for executives
df_monthly_summary = df_sales_fact.groupBy(
    "OrderYear", "OrderMonth", "OrderMonthName", "Category", "Country"
).agg(
    sum("SalesAmount").alias("TotalSales"),
    sum("OrderQuantity").alias("TotalQuantity"),
    sum("GrossProfit").alias("TotalProfit"),
    count("*").alias("OrderCount"),
    countDistinct("ResellerKey").alias("CustomerCount"),
    countDistinct("ProductKey").alias("ProductCount"),
    avg("SalesAmount").alias("AvgOrderValue")
).withColumn(
    "ProfitMargin",
    when(col("TotalSales") > 0, col("TotalProfit") / col("TotalSales") * 100).otherwise(0)
)

print("✅ Monthly Summary created")
print(f"Records: {df_monthly_summary.count():,}")
df_monthly_summary.orderBy(desc("OrderYear"), desc("OrderMonth")).show(5)

# Simple product performance table
df_product_performance = df_sales_fact.groupBy(
    "ProductKey", "ProductName", "Category", "Subcategory", "Color"
).agg(
    sum("SalesAmount").alias("TotalSales"),
    sum("OrderQuantity").alias("TotalQuantity"),
    sum("GrossProfit").alias("TotalProfit"),
    count("*").alias("OrderCount"),
    countDistinct("ResellerKey").alias("CustomerCount"),
    avg("SalesAmount").alias("AvgOrderValue"),
    first("StandardCost").alias("StandardCost")
).withColumn(
    "ProfitMargin",
    when(col("TotalSales") > 0, col("TotalProfit") / col("TotalSales") * 100).otherwise(0)
)

print("✅ Product Performance created")
print(f"Records: {df_product_performance.count():,}")
df_product_performance.orderBy(desc("TotalSales")).show(5)

# Simple customer analysis
df_customer_analysis = df_sales_fact.groupBy(
    "ResellerKey", "CustomerName", "BusinessType", "City", "State", "Country"
).agg(
    sum("SalesAmount").alias("TotalSales"),
    sum("OrderQuantity").alias("TotalQuantity"),
    sum("GrossProfit").alias("TotalProfit"),
    count("*").alias("OrderCount"),
    countDistinct("ProductKey").alias("ProductCount"),
    avg("SalesAmount").alias("AvgOrderValue"),
    min("OrderDate").alias("FirstOrderDate"),
    max("OrderDate").alias("LastOrderDate")
).withColumn(
    "ProfitMargin",
    when(col("TotalSales") > 0, col("TotalProfit") / col("TotalSales") * 100).otherwise(0)
)

print("✅ Customer Analysis created")
print(f"Records: {df_customer_analysis.count():,}")
df_customer_analysis.orderBy(desc("TotalSales")).show(5)

# Simple salesperson performance
df_salesperson_performance = df_sales_fact.groupBy(
    "SalespersonKey", "SalespersonName", "JobTitle", "OrderYear", "OrderMonth"
).agg(
    sum("SalesAmount").alias("TotalSales"),
    sum("OrderQuantity").alias("TotalQuantity"),
    sum("GrossProfit").alias("TotalProfit"),
    count("*").alias("OrderCount"),
    countDistinct("ResellerKey").alias("CustomerCount"),
    countDistinct("ProductKey").alias("ProductCount"),
    avg("SalesAmount").alias("AvgOrderValue")
).withColumn(
    "ProfitMargin",
    when(col("TotalSales") > 0, col("TotalProfit") / col("TotalSales") * 100).otherwise(0)
)

print("✅ Salesperson Performance created")
print(f"Records: {df_salesperson_performance.count():,}")
df_salesperson_performance.orderBy(desc("OrderYear"), desc("OrderMonth"), desc("TotalSales")).show(5)

# Simple function to save tables
def save_gold_table(df, table_name):
    try:
        full_table_name = f"{catalog}.{gold_schema}.{table_name}"
        
        df.write \
          .format("delta") \
          .mode("overwrite") \
          .option("mergeSchema", "true") \
          .saveAsTable(full_table_name)
        
        print(f"✅ Saved {table_name} to {full_table_name}")
        return True
    except Exception as e:
        print(f"❌ Error saving {table_name}: {str(e)}")
        return False

# Save all Gold layer tables
print("\n💾 Saving Gold layer tables...")

tables_to_save = [
    (df_sales_fact, "sales_fact"),
    (df_monthly_summary, "monthly_summary"),
    (df_product_performance, "product_performance"),
    (df_customer_analysis, "customer_analysis"),
    (df_salesperson_performance, "salesperson_performance")
]

success_count = 0
for df, table_name in tables_to_save:
    if save_gold_table(df, table_name):
        success_count += 1

print(f"\n🎉 Gold Layer Complete!")
print(f"✅ Successfully saved {success_count}/{len(tables_to_save)} tables")
print(f"\n📊 Tables ready for Power BI:")
print(f"   • {catalog}.{gold_schema}.sales_fact")
print(f"   • {catalog}.{gold_schema}.monthly_summary")
print(f"   • {catalog}.{gold_schema}.product_performance")
print(f"   • {catalog}.{gold_schema}.customer_analysis")
print(f"   • {catalog}.{gold_schema}.salesperson_performance")