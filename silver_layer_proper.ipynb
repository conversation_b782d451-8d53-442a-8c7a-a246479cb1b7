{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Adventure Works Silver Layer - Proper Data Cleaning & Standardization\n", "\n", "This notebook implements **proper Silver Layer** transformations following Medallion Architecture best practices:\n", "\n", "## Silver Layer Focus:\n", "- 🧹 **Data Quality** - Cleaning, validation, deduplication\n", "- 📏 **Standardization** - Consistent formats, naming conventions\n", "- 🔗 **Basic Enrichment** - Simple lookups and mappings\n", "- ✅ **Data Validation** - Schema enforcement, constraint checks\n", "- 📊 **Basic Metrics** - Simple calculations for data quality\n", "\n", "## What's NOT in Silver:\n", "- ❌ Complex business logic\n", "- ❌ Advanced aggregations\n", "- ❌ ML feature engineering\n", "- ❌ Business-specific KPIs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql.functions import *\n", "from pyspark.sql.types import *\n", "from pyspark.sql.window import Window"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration\n", "catalog = \"adventworks\"\n", "bronze_schema = \"bronze_schema\"\n", "silver_schema = \"silver_schema\"\n", "\n", "spark.sql(f\"USE CATALOG {catalog}\")\n", "spark.sql(f\"CREATE SCHEMA IF NOT EXISTS {catalog}.{silver_schema}\")\n", "\n", "print(f\"✅ Silver schema {catalog}.{silver_schema} ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Product Data - Cleaning & Standardization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read bronze product data\n", "df_product_bronze = spark.table(f\"{catalog}.{bronze_schema}.product\")\n", "\n", "# Silver layer: Clean and standardize product data\n", "df_product_silver = df_product_bronze.select(\n", "    col(\"ProductKey\"),\n", "    \n", "    # 🧹 DATA CLEANING\n", "    trim(col(\"Product\")).alias(\"ProductName\"),\n", "    trim(col(\"Category\")).alias(\"Category\"),\n", "    trim(col(\"Subcategory\")).alias(\"Subcategory\"),\n", "    \n", "    # 📏 STANDARDIZATION\n", "    when(col(\"Color\").isNull() | (col(\"Color\") == \"\") | (col(\"Color\") == \"NA\"), \"Unknown\")\n", "    .otherwise(trim(upper(col(\"Color\")))).alias(\"Color\"),\n", "    \n", "    # 💰 COST VALIDATION & CLEANING\n", "    when(col(\"Standard_Cost\").isNull() | (col(\"Standard_Cost\") < 0), 0.0)\n", "    .otherwise(round(col(\"Standard_Cost\"), 2)).alias(\"StandardCost\"),\n", "    \n", "    # 🎨 COLOR FORMATTING (keep existing good work)\n", "    col(\"Background_Color_Format\").alias(\"BackgroundColorFormat\"),\n", "    col(\"Font_Color_Format\").alias(\"FontColorFormat\"),\n", "    \n", "    # ✅ DATA QUALITY FLAGS\n", "    when(col(\"Standard_Cost\").isNull() | (col(\"Standard_Cost\") <= 0), \"INVALID_COST\")\n", "    .when(col(\"Product\").isNull() | (col(\"Product\") == \"\"), \"MISSING_NAME\")\n", "    .when(col(\"Category\").isNull(), \"MISSING_CATEGORY\")\n", "    .otherwise(\"VALID\").alias(\"DataQualityStatus\"),\n", "    \n", "    # 📊 METADATA\n", "    current_timestamp().alias(\"ProcessedAt\"),\n", "    lit(\"silver_v1.0\").alias(\"ProcessingVersion\")\n", ").filter(\n", "    # Remove completely invalid records\n", "    col(\"ProductKey\").isNotNull() & \n", "    (col(\"Product\").isNotNull()) & \n", "    (col(\"Product\") != \"\")\n", ")\n", "\n", "print(\"✅ Product Silver layer completed\")\n", "print(f\"Records processed: {df_product_silver.count():,}\")\n", "\n", "# Data quality summary\n", "print(\"\\nData Quality Summary:\")\n", "df_product_silver.groupBy(\"DataQualityStatus\").count().show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Sales Data - Cleaning & Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read bronze sales data\n", "df_sales_bronze = spark.table(f\"{catalog}.{bronze_schema}.sales\")\n", "\n", "# Silver layer: Clean and validate sales data\n", "df_sales_silver = df_sales_bronze.select(\n", "    col(\"SalesOrderNumber\"),\n", "    col(\"OrderDate\"),\n", "    col(\"ProductKey\"),\n", "    col(\"ResellerKey\"),\n", "    col(\"EmployeeKey\"),\n", "    col(\"SalesTerritoryKey\"),\n", "    \n", "    # 🧹 NUMERIC DATA CLEANING\n", "    when(col(\"Quantity\").isNull() | (col(\"Quantity\") <= 0), 1)\n", "    .otherwise(col(\"Quantity\")).alias(\"OrderQuantity\"),\n", "    \n", "    when(col(\"Unit_Price\").isNull() | (col(\"Unit_Price\") <= 0), 0.0)\n", "    .otherwise(round(col(\"Unit_Price\"), 2)).alias(\"UnitPrice\"),\n", "    \n", "    when(col(\"Sales\").isNull() | (col(\"Sales\") <= 0), 0.0)\n", "    .otherwise(round(col(\"Sales\"), 2)).alias(\"SalesAmount\"),\n", "    \n", "    # 📅 DATE STANDARDIZATION\n", "    to_date(col(\"OrderDate\")).alias(\"OrderDate\"),\n", "    year(col(\"OrderDate\")).alias(\"OrderYear\"),\n", "    month(col(\"OrderDate\")).alias(\"Order<PERSON><PERSON><PERSON>\"),\n", "    quarter(col(\"OrderDate\")).alias(\"OrderQuarter\"),\n", "    \n", "    # ✅ DATA VALIDATION\n", "    when(col(\"Sales\").isNull() | (col(\"Sales\") <= 0), \"INVALID_SALES\")\n", "    .when(col(\"Quantity\").isNull() | (col(\"Quantity\") <= 0), \"INVALID_QUANTITY\")\n", "    .when(col(\"Unit_Price\").isNull() | (col(\"Unit_Price\") <= 0), \"INVALID_PRICE\")\n", "    .when(col(\"OrderDate\").isNull(), \"INVALID_DATE\")\n", "    .otherwise(\"VALID\").alias(\"DataQualityStatus\"),\n", "    \n", "    # 🔍 BUSINESS RULE VALIDATION\n", "    when(abs(col(\"Sales\") - (col(\"Quantity\") * col(\"Unit_Price\"))) > 0.01, \"CALCULATION_MISMATCH\")\n", "    .otherwise(\"CALCULATION_OK\").alias(\"BusinessRuleStatus\"),\n", "    \n", "    # 📊 METADATA\n", "    current_timestamp().alias(\"ProcessedAt\"),\n", "    lit(\"silver_v1.0\").alias(\"ProcessingVersion\")\n", ").filter(\n", "    # Remove completely invalid records\n", "    col(\"SalesOrderNumber\").isNotNull() &\n", "    col(\"ProductKey\").isNotNull() &\n", "    col(\"OrderDate\").isNotNull()\n", ")\n", "\n", "print(\"✅ Sales Silver layer completed\")\n", "print(f\"Records processed: {df_sales_silver.count():,}\")\n", "\n", "# Data quality summary\n", "print(\"\\nData Quality Summary:\")\n", "df_sales_silver.groupBy(\"DataQualityStatus\").count().show()\n", "print(\"\\nBusiness Rule Validation:\")\n", "df_sales_silver.groupBy(\"BusinessRuleStatus\").count().show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Customer/Reseller Data - Standardization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read bronze reseller data (acting as customer data)\n", "df_reseller_bronze = spark.table(f\"{catalog}.{bronze_schema}.reseller\")\n", "\n", "# Silver layer: Standardize customer/reseller data\n", "df_reseller_silver = df_reseller_bronze.select(\n", "    col(\"ResellerKey\"),\n", "    \n", "    # 🧹 TEXT CLEANING & STANDARDIZATION\n", "    trim(col(\"Reseller\")).alias(\"ResellerName\"),\n", "    trim(upper(col(\"Business_Type\"))).alias(\"BusinessType\"),\n", "    trim(initcap(col(\"City\"))).alias(\"City\"),\n", "    trim(upper(col(\"State_Province\"))).alias(\"StateProvince\"),\n", "    trim(upper(col(\"Country_Region\"))).alias(\"CountryRegion\"),\n", "    \n", "    # 🌍 GEOGRAPHIC STANDARDIZATION\n", "    when(col(\"Country_Region\").isin([\"US\", \"USA\", \"UNITED STATES\"]), \"UNITED STATES\")\n", "    .when(col(\"Country_Region\").isin([\"UK\", \"UNITED KINGDOM\"]), \"UNITED KINGDOM\")\n", "    .when(col(\"Country_Region\").isin([\"CA\", \"CANADA\"]), \"CANADA\")\n", "    .otherwise(trim(upper(col(\"Country_Region\")))).alias(\"StandardizedCountry\"),\n", "    \n", "    # ✅ DATA QUALITY VALIDATION\n", "    when(col(\"Reseller\").isNull() | (col(\"Reseller\") == \"\"), \"MISSING_NAME\")\n", "    .when(col(\"City\").isNull() | (col(\"City\") == \"\"), \"MISSING_CITY\")\n", "    .when(col(\"Country_Region\").isNull(), \"MISSING_COUNTRY\")\n", "    .otherwise(\"VALID\").alias(\"DataQualityStatus\"),\n", "    \n", "    # 📊 METADATA\n", "    current_timestamp().alias(\"ProcessedAt\"),\n", "    lit(\"silver_v1.0\").alias(\"ProcessingVersion\")\n", ").filter(\n", "    col(\"ResellerKey\").isNotNull()\n", ")\n", "\n", "print(\"✅ Reseller Silver layer completed\")\n", "print(f\"Records processed: {df_reseller_silver.count():,}\")\n", "\n", "# Data quality summary\n", "print(\"\\nData Quality Summary:\")\n", "df_reseller_silver.groupBy(\"DataQualityStatus\").count().show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Employee/Salesperson Data - Cleaning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read bronze salesperson data\n", "df_salesperson_bronze = spark.table(f\"{catalog}.{bronze_schema}.salesperson\")\n", "\n", "# Silver layer: Clean and standardize employee data\n", "df_salesperson_silver = df_salesperson_bronze.select(\n", "    col(\"EmployeeKey\"),\n", "    trim(col(\"Employee<PERSON>\")).alias(\"Employee<PERSON>\"),\n", "    \n", "    # 🧹 NAME CLEANING\n", "    trim(initcap(col(\"Salesperson\"))).alias(\"Salesperson<PERSON><PERSON>\"),\n", "    trim(col(\"Title\")).alias(\"<PERSON>T<PERSON><PERSON>\"),\n", "    \n", "    # 📧 EMAIL STANDARDIZATION\n", "    lower(trim(col(\"UPN\"))).alias(\"<PERSON>ail<PERSON><PERSON><PERSON>\"),\n", "    \n", "    # 🏢 EMAIL DOMAIN EXTRACTION\n", "    regexp_extract(lower(col(\"UPN\")), \"@(.+)\", 1).alias(\"EmailDomain\"),\n", "    \n", "    # ✅ DATA QUALITY VALIDATION\n", "    when(col(\"Salesperson\").isNull() | (col(\"Salesperson\") == \"\"), \"MISSING_NAME\")\n", "    .when(col(\"UPN\").isNull() | (col(\"UPN\") == \"\"), \"MISSING_EMAIL\")\n", "    .when(col(\"Title\").isNull(), \"MISSING_TITLE\")\n", "    .when(!col(\"UPN\").contains(\"@\"), \"INVALID_EMAIL\")\n", "    .otherwise(\"VALID\").alias(\"DataQualityStatus\"),\n", "    \n", "    # 📊 METADATA\n", "    current_timestamp().alias(\"ProcessedAt\"),\n", "    lit(\"silver_v1.0\").alias(\"ProcessingVersion\")\n", ").filter(\n", "    col(\"EmployeeKey\").isNotNull()\n", ")\n", "\n", "print(\"✅ Salesperson Silver layer completed\")\n", "print(f\"Records processed: {df_salesperson_silver.count():,}\")\n", "\n", "# Data quality summary\n", "print(\"\\nData Quality Summary:\")\n", "df_salesperson_silver.groupBy(\"DataQualityStatus\").count().show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Targets Data - Validation & Standardization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read bronze targets data\n", "df_targets_bronze = spark.table(f\"{catalog}.{bronze_schema}.targets\")\n", "\n", "# Silver layer: Clean and validate targets data\n", "df_targets_silver = df_targets_bronze.select(\n", "    trim(col(\"Employee<PERSON>\")).alias(\"Employee<PERSON>\"),\n", "    \n", "    # 💰 TARGET AMOUNT CLEANING\n", "    when(col(\"Target\").isNull() | (col(\"Target\") <= 0), 0.0)\n", "    .otherwise(round(col(\"Target\"), 2)).alias(\"TargetAmount\"),\n", "    \n", "    # 📅 DATE STANDARDIZATION\n", "    to_date(col(\"TargetMonth\")).alias(\"TargetMonth\"),\n", "    year(col(\"Target<PERSON><PERSON><PERSON>\")).alias(\"TargetYear\"),\n", "    month(col(\"TargetMonth\")).alias(\"TargetMonthNumber\"),\n", "    quarter(col(\"TargetMonth\")).alias(\"TargetQuarter\"),\n", "    \n", "    # ✅ DATA QUALITY VALIDATION\n", "    when(col(\"EmployeeID\").isNull() | (col(\"EmployeeID\") == \"\"), \"MISSING_EMPLOYEE\")\n", "    .when(col(\"Target\").isNull() | (col(\"Target\") <= 0), \"INVALID_TARGET\")\n", "    .when(col(\"TargetMonth\").isNull(), \"MISSING_DATE\")\n", "    .otherwise(\"VALID\").alias(\"DataQualityStatus\"),\n", "    \n", "    # 📊 METADATA\n", "    current_timestamp().alias(\"ProcessedAt\"),\n", "    lit(\"silver_v1.0\").alias(\"ProcessingVersion\")\n", ").filter(\n", "    col(\"EmployeeID\").isNotNull() & \n", "    (col(\"EmployeeID\") != \"\") &\n", "    col(\"TargetMonth\").isNotNull()\n", ")\n", "\n", "print(\"✅ Targets Silver layer completed\")\n", "print(f\"Records processed: {df_targets_silver.count():,}\")\n", "\n", "# Data quality summary\n", "print(\"\\nData Quality Summary:\")\n", "df_targets_silver.groupBy(\"DataQualityStatus\").count().show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Save Silver Layer Tables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save all Silver layer tables\n", "silver_tables = [\n", "    (df_product_silver, \"product\", None),\n", "    (df_sales_silver, \"sales\", [\"OrderYear\", \"OrderMonth\"]),\n", "    (df_reseller_silver, \"reseller\", [\"StandardizedCountry\"]),\n", "    (df_salesperson_silver, \"salesperson\", None),\n", "    (df_targets_silver, \"targets\", [\"TargetYear\", \"TargetMonthNumber\"])\n", "]\n", "\n", "for df, table_name, partition_cols in silver_tables:\n", "    print(f\"\\n💾 Saving {table_name} to Silver schema...\")\n", "    \n", "    writer = df.write.format(\"delta\").mode(\"overwrite\").option(\"mergeSchema\", \"true\")\n", "    \n", "    if partition_cols:\n", "        writer = writer.partitionBy(*partition_cols)\n", "        print(f\"   📁 Partitioned by: {partition_cols}\")\n", "    \n", "    writer.saveAsTable(f\"{catalog}.{silver_schema}.{table_name}\")\n", "    \n", "    # Optimize and analyze\n", "    spark.sql(f\"OPTIMIZE {catalog}.{silver_schema}.{table_name}\")\n", "    spark.sql(f\"ANALYZE TABLE {catalog}.{silver_schema}.{table_name} COMPUTE STATISTICS\")\n", "    \n", "    print(f\"   ✅ {table_name} saved and optimized\")\n", "\n", "print(\"\\n🎉 Silver Layer Complete!\")\n", "print(\"\\n✅ Data is now:\")\n", "print(\"   • Cleaned and validated\")\n", "print(\"   • Standardized and consistent\")\n", "print(\"   • Ready for Gold layer aggregations\")\n", "print(\"   • Optimized for performance\")\n", "print(\"\\n🚀 Ready for Gold layer business analytics!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}