from pyspark.sql.functions import *
from pyspark.sql.types import *

# Catalog and schema configuration
catalog = "adventworks"
bronze_schema = "bronze_schema"
spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {bronze_schema}")

df_product = spark.table('adventworks.bronze_schema.product')
df_product.display()

df_product_drop_color_formats = df_product.drop("Background_Color_Format", "Font_Color_Format")
df_product_drop_color_formats.display()

df_region = spark.table('adventworks.bronze_schema.region')
df_region.display()

df_reseller = spark.table('adventworks.bronze_schema.reseller')
df_reseller.display()

df_reseller = df_reseller.withColumnRenamed("State_Province", "Province")\
                        .withColumnRenamed("Country_Region", "Country")
df_reseller.display()


df_sales = spark.table('adventworks.bronze_schema.sales')
df_sales.display()

df_salesperson = spark.table('adventworks.bronze_schema.salesperson')
df_salesperson.display()

df_salesperson_region = spark.table('adventworks.bronze_schema.salespersonregion')
df_salesperson_region.display()

df_targets = spark.table('adventworks.bronze_schema.targets')
df_targets.display()

