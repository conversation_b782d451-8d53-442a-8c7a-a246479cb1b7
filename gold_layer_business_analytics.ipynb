{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🥇 Adventure Works - Gold Layer Business Analytics\n", "\n", "This notebook creates **Gold layer** tables optimized for:\n", "- 📊 **Business Intelligence** dashboards\n", "- 🎯 **Executive KPIs** and reporting\n", "- 📈 **Advanced Analytics** for decision making\n", "- 🤖 **ML-ready datasets** for data science teams\n", "\n", "## Prerequisites:\n", "- Silver layer tables must be created first\n", "- Tables: `adventworks.silver_schema.{sales, product, reseller, salesperson, targets}`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql.functions import *\n", "from pyspark.sql.types import *\n", "from pyspark.sql.window import Window\n", "\n", "# Configuration\n", "catalog = \"adventworks\"\n", "silver_schema = \"silver_schema\"\n", "gold_schema = \"gold_schema\"\n", "\n", "# Create Gold schema\n", "spark.sql(f\"CREATE SCHEMA IF NOT EXISTS {catalog}.{gold_schema}\")\n", "\n", "print(f\"✅ Gold schema {catalog}.{gold_schema} ready\")\n", "print(f\"📊 Reading from Silver: {catalog}.{silver_schema}\")\n", "print(f\"🥇 Writing to Gold: {catalog}.{gold_schema}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load Silver layer tables\n", "df_sales_silver = spark.table(f\"{catalog}.{silver_schema}.sales\")\n", "df_product_silver = spark.table(f\"{catalog}.{silver_schema}.product\")\n", "df_reseller_silver = spark.table(f\"{catalog}.{silver_schema}.reseller\")\n", "df_salesperson_silver = spark.table(f\"{catalog}.{silver_schema}.salesperson\")\n", "df_targets_silver = spark.table(f\"{catalog}.{silver_schema}.targets\")\n", "\n", "print(\"✅ Silver layer tables loaded\")\n", "print(f\"Sales records: {df_sales_silver.count():,}\")\n", "print(f\"Products: {df_product_silver.count():,}\")\n", "print(f\"Resellers: {df_reseller_silver.count():,}\")\n", "print(f\"Salespeople: {df_salesperson_silver.count():,}\")\n", "print(f\"Targets: {df_targets_silver.count():,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Executive KPI Dashboard - Monthly Business Metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Executive KPI Dashboard - Monthly aggregated metrics\n", "df_executive_kpis = df_sales_silver.join(df_product_silver, \"ProductKey\") \\\n", "    .select(\n", "        col(\"OrderYear\").alias(\"Year\"),\n", "        col(\"Order<PERSON><PERSON><PERSON>\").alias(\"Month\"),\n", "        col(\"Category\"),\n", "        col(\"Subcategory\"),\n", "        col(\"SalesAmount\").alias(\"Revenue\"),\n", "        col(\"OrderQuantity\").alias(\"Quantity\"),\n", "        col(\"StandardCost\"),\n", "        col(\"ResellerKey\"),\n", "        col(\"SalespersonKey\")\n", "    ).groupBy(\"Year\", \"Month\", \"Category\", \"Subcategory\").agg(\n", "        # 💰 FINANCIAL KPIs\n", "        sum(\"Revenue\").alias(\"TotalRevenue\"),\n", "        sum(\"Quantity\").alias(\"TotalUnitsSold\"),\n", "        sum(col(\"Quantity\") * col(\"StandardCost\")).alias(\"TotalCOGS\"),\n", "        count(\"*\").alias(\"TotalOrders\"),\n", "        countDistinct(\"Reseller<PERSON><PERSON>\").alias(\"ActiveCustomers\"),\n", "        countDistinct(\"Salesperson<PERSON><PERSON>\").alias(\"ActiveSalespeople\"),\n", "        \n", "        # 📊 BUSINESS METRICS\n", "        avg(\"Revenue\").alias(\"AvgOrderValue\"),\n", "        avg(\"Quantity\").alias(\"AvgOrderSize\")\n", "    ).withColumn(\n", "        # 💡 CALCULATED KPIs\n", "        \"GrossProfit\", col(\"TotalRevenue\") - col(\"TotalCOGS\")\n", "    ).withColumn(\n", "        \"GrossProfitMargin\", \n", "        when(col(\"TotalRevenue\") > 0, col(\"GrossProfit\") / col(\"TotalRevenue\") * 100)\n", "        .otherwise(0)\n", "    ).withColumn(\n", "        \"RevenuePerCustomer\",\n", "        when(col(\"ActiveCustomers\") > 0, col(\"TotalRevenue\") / col(\"ActiveCustomers\"))\n", "        .otherwise(0)\n", "    ).withColumn(\n", "        \"RevenuePerSalesperson\",\n", "        when(col(\"ActiveSalespeople\") > 0, col(\"TotalRevenue\") / col(\"ActiveSalespeople\"))\n", "        .otherwise(0)\n", "    ).withColumn(\n", "        # 📅 TIME DIMENSIONS\n", "        \"Year<PERSON><PERSON>h\", concat(col(\"Year\"), lit(\"-\"), lpad(col(\"Month\"), 2, \"0\"))\n", "    ).withColumn(\n", "        \"Quarter\", \n", "        when(col(\"Month\").isin([1,2,3]), \"Q1\")\n", "        .when(col(\"Month\").isin([4,5,6]), \"Q2\")\n", "        .when(col(\"Month\").isin([7,8,9]), \"Q3\")\n", "        .otherwise(\"Q4\")\n", "    ).withColumn(\n", "        \"ProcessedTimestamp\", current_timestamp()\n", "    )\n", "\n", "print(\"✅ Executive KPI Dashboard created\")\n", "print(f\"Records: {df_executive_kpis.count():,}\")\n", "df_executive_kpis.orderBy(desc(\"Year\"), desc(\"Month\"), desc(\"TotalRevenue\")).show(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Customer Analytics - RFM Segmentation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Customer RFM Analysis (using ResellerKey as customer proxy)\n", "analysis_date = df_sales_silver.agg(max(\"OrderDate\")).collect()[0][0]\n", "\n", "df_customer_rfm = df_sales_silver.groupBy(\"ResellerKey\").agg(\n", "    # 📅 RECENCY (days since last purchase)\n", "    datediff(lit(analysis_date), max(\"OrderDate\")).alias(\"Recency\"),\n", "    \n", "    # 🔄 FREQUENCY (number of purchases)\n", "    count(\"*\").alias(\"Frequency\"),\n", "    \n", "    # 💰 MONETARY (total spent)\n", "    sum(\"SalesAmount\").alias(\"Monetary\"),\n", "    \n", "    # 📊 ADDITIONAL METRICS\n", "    avg(\"SalesAmount\").alias(\"AvgOrderValue\"),\n", "    countDistinct(\"ProductKey\").alias(\"ProductDiversity\"),\n", "    min(\"OrderDate\").alias(\"FirstPurchaseDate\"),\n", "    max(\"OrderDate\").alias(\"LastPurchaseDate\")\n", ").withColumn(\n", "    \"CustomerLifespanDays\",\n", "    datediff(col(\"LastPurchaseDate\"), col(\"FirstPurchaseDate\"))\n", ")\n", "\n", "# Calculate RFM Scores (1-5 scale)\n", "recency_quantiles = df_customer_rfm.approxQuantile(\"Recency\", [0.2, 0.4, 0.6, 0.8], 0.01)\n", "frequency_quantiles = df_customer_rfm.approxQuantile(\"Frequency\", [0.2, 0.4, 0.6, 0.8], 0.01)\n", "monetary_quantiles = df_customer_rfm.approxQuantile(\"Monetary\", [0.2, 0.4, 0.6, 0.8], 0.01)\n", "\n", "df_customer_segments = df_customer_rfm.withColumn(\n", "    \"R_Score\",\n", "    when(col(\"Recency\") <= recency_quantiles[0], 5)\n", "    .when(col(\"Recency\") <= recency_quantiles[1], 4)\n", "    .when(col(\"Recency\") <= recency_quantiles[2], 3)\n", "    .when(col(\"Recency\") <= recency_quantiles[3], 2)\n", "    .otherwise(1)\n", ").withColumn(\n", "    \"F_Score\",\n", "    when(col(\"Frequency\") >= frequency_quantiles[3], 5)\n", "    .when(col(\"Frequency\") >= frequency_quantiles[2], 4)\n", "    .when(col(\"Frequency\") >= frequency_quantiles[1], 3)\n", "    .when(col(\"Frequency\") >= frequency_quantiles[0], 2)\n", "    .otherwise(1)\n", ").withColumn(\n", "    \"M_Score\",\n", "    when(col(\"Monetary\") >= monetary_quantiles[3], 5)\n", "    .when(col(\"Monetary\") >= monetary_quantiles[2], 4)\n", "    .when(col(\"Monetary\") >= monetary_quantiles[1], 3)\n", "    .when(col(\"Monetary\") >= monetary_quantiles[0], 2)\n", "    .otherwise(1)\n", ").withColumn(\n", "    \"RFM_Score\", concat(col(\"R_Score\"), col(\"F_Score\"), col(\"M_Score\"))\n", ").withColumn(\n", "    # 🎯 BUSINESS SEGMENTS\n", "    \"CustomerSegment\",\n", "    when((col(\"R_Score\") >= 4) & (col(\"F_Score\") >= 4) & (col(\"M_Score\") >= 4), \"Champions\")\n", "    .when((col(\"R_Score\") >= 3) & (col(\"F_Score\") >= 3) & (col(\"M_Score\") >= 4), \"Loyal Customers\")\n", "    .when((col(\"R_Score\") >= 4) & (col(\"F_Score\") <= 2), \"New Customers\")\n", "    .when((col(\"R_Score\") >= 3) & (col(\"F_Score\") >= 3) & (col(\"M_Score\") <= 3), \"Potential Loyalists\")\n", "    .when((col(\"R_Score\") <= 2) & (col(\"F_Score\") >= 3) & (col(\"M_Score\") >= 3), \"At Risk\")\n", "    .when((col(\"R_Score\") <= 2) & (col(\"F_Score\") <= 2) & (col(\"M_Score\") >= 4), \"Can't Lose Them\")\n", "    .when((col(\"R_Score\") <= 2) & (col(\"F_Score\") <= 2) & (col(\"M_Score\") <= 2), \"Lost Customers\")\n", "    .otherwise(\"Others\")\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ Customer RFM Analysis completed\")\n", "print(f\"Total customers: {df_customer_segments.count():,}\")\n", "print(\"\\nCustomer Segments Distribution:\")\n", "df_customer_segments.groupBy(\"CustomerSegment\").agg(\n", "    count(\"*\").alias(\"CustomerCount\"),\n", "    avg(\"Monetary\").alias(\"AvgMonetaryValue\")\n", ").orderBy(desc(\"CustomerCount\")).show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Product Performance Analytics - ABC Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Product ABC Analysis for Inventory Management\n", "df_product_performance = df_sales_silver.join(df_product_silver, \"ProductKey\").groupBy(\n", "    \"ProductKey\", \"ProductName\", \"Category\", \"Subcategory\", \"StandardCost\"\n", ").agg(\n", "    sum(\"SalesAmount\").alias(\"TotalRevenue\"),\n", "    sum(\"OrderQuantity\").alias(\"TotalQuantitySold\"),\n", "    count(\"*\").alias(\"TotalOrders\"),\n", "    countDistinct(\"Reseller<PERSON><PERSON>\").alias(\"UniqueCustomers\"),\n", "    avg(\"SalesAmount\").alias(\"AvgOrderValue\"),\n", "    stddev(\"SalesAmount\").alias(\"RevenueStdDev\")\n", ").withColumn(\n", "    \"TotalCost\", col(\"TotalQuantitySold\") * col(\"StandardCost\")\n", ").withColumn(\n", "    \"GrossProfit\", col(\"TotalRevenue\") - col(\"TotalCost\")\n", ").withColumn(\n", "    \"ProfitMargin\",\n", "    when(col(\"TotalRevenue\") > 0, col(\"GrossProfit\") / col(\"TotalRevenue\") * 100)\n", "    .otherwise(0)\n", ")\n", "\n", "# Calculate cumulative revenue percentage for ABC classification\n", "total_revenue = df_product_performance.agg(sum(\"TotalRevenue\")).collect()[0][0]\n", "\n", "window_spec = Window.orderBy(desc(\"TotalRevenue\"))\n", "df_abc_analysis = df_product_performance.withColumn(\n", "    \"RevenueRank\", row_number().over(window_spec)\n", ").withColumn(\n", "    \"CumulativeRevenue\", sum(\"TotalRevenue\").over(window_spec.rowsBetween(Window.unboundedPreceding, 0))\n", ").withColumn(\n", "    \"CumulativeRevenuePercent\", col(\"CumulativeRevenue\") / total_revenue * 100\n", ").withColumn(\n", "    # 📊 ABC CLASSIFICATION\n", "    \"ABC_Category\",\n", "    when(col(\"CumulativeRevenuePercent\") <= 80, \"A - High Value\")\n", "    .when(col(\"CumulativeRevenuePercent\") <= 95, \"B - Medium Value\")\n", "    .otherwise(\"C - Low Value\")\n", ").withColumn(\n", "    # 🎯 BUSINESS RECOMMENDATIONS\n", "    \"InventoryStrategy\",\n", "    when(col(\"ABC_Category\") == \"A - High Value\", \"High Stock - Frequent Review\")\n", "    .when(col(\"ABC_Category\") == \"B - Medium Value\", \"Moderate Stock - Regular Review\")\n", "    .otherwise(\"Low Stock - Periodic Review\")\n", ").withColumn(\n", "    \"MarketingPriority\",\n", "    when((col(\"ABC_Category\") == \"A - High Value\") & (col(\"ProfitMargin\") >= 30), \"High Priority\")\n", "    .when(col(\"ABC_Category\") == \"A - High Value\", \"Medium Priority\")\n", "    .otherwise(\"Low Priority\")\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ Product ABC Analysis completed\")\n", "print(f\"Total products: {df_abc_analysis.count():,}\")\n", "print(\"\\nABC Distribution:\")\n", "df_abc_analysis.groupBy(\"ABC_Category\").agg(\n", "    count(\"*\").alias(\"ProductCount\"),\n", "    sum(\"TotalRevenue\").alias(\"CategoryRevenue\"),\n", "    avg(\"ProfitMargin\").alias(\"AvgProfitMargin\")\n", ").show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Sales Performance Scorecard"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sales Performance Scorecard with Target Achievement\n", "# Simplified approach - Monthly sales performance by salesperson\n", "df_sales_performance = df_sales_silver.join(\n", "    df_salesperson_silver, \"SalespersonKey\"\n", ").select(\n", "    col(\"EmployeeID\"),\n", "    col(\"SalespersonName\"),\n", "    col(\"Title\").alias(\"<PERSON><PERSON><PERSON><PERSON>\"),\n", "    col(\"OrderYear\").alias(\"Year\"),\n", "    col(\"Order<PERSON><PERSON><PERSON>\").alias(\"Month\"),\n", "    col(\"SalesAmount\").alias(\"Revenue\")\n", ").groupBy(\n", "    \"EmployeeID\", \"SalespersonName\", \"JobTitle\", \"Year\", \"Month\"\n", ").agg(\n", "    sum(\"Revenue\").alias(\"ActualSales\"),\n", "    count(\"*\").alias(\"OrdersCount\")\n", ").withColumn(\n", "    # 🏆 RANKINGS\n", "    \"SalesRank\",\n", "    row_number().over(Window.partitionBy(\"Year\", \"Month\").orderBy(desc(\"ActualSales\")))\n", ").withColumn(\n", "    # 📊 PERFORMANCE CATEGORIES\n", "    \"PerformanceCategory\",\n", "    when(col(\"SalesRank\") <= 3, \"Top Performer\")\n", "    .when(col(\"SalesRank\") <= 10, \"High Performer\")\n", "    .when(col(\"SalesRank\") <= 20, \"Average Performer\")\n", "    .otherwise(\"Needs Improvement\")\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ Sales Performance Scorecard created\")\n", "print(f\"Performance records: {df_sales_performance.count():,}\")\n", "print(\"\\nPerformance Distribution:\")\n", "df_sales_performance.groupBy(\"PerformanceCategory\").agg(\n", "    count(\"*\").alias(\"Count\"),\n", "    avg(\"ActualSales\").alias(\"AvgSales\")\n", ").orderBy(desc(\"Count\")).show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Time Series Data for ML & Forecasting"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Time Series Dataset for Data Scientists and ML Engineers\n", "df_time_series = df_sales_silver.join(df_product_silver, \"ProductKey\") \\\n", "    .select(\n", "        col(\"OrderDate\"),\n", "        col(\"SalesAmount\").alias(\"Revenue\"),\n", "        col(\"OrderQuantity\").alias(\"Quantity\"),\n", "        col(\"Category\"),\n", "        col(\"Subcategory\")\n", "    ).groupBy(\"OrderDate\", \"Category\", \"Subcategory\").agg(\n", "        sum(\"Revenue\").alias(\"DailyRevenue\"),\n", "        sum(\"Quantity\").alias(\"DailyQuantity\"),\n", "        count(\"*\").alias(\"DailyOrders\")\n", "    )\n", "\n", "# Add time-based features for ML\n", "df_ml_features = df_time_series.withColumn(\n", "    \"Year\", year(col(\"OrderDate\"))\n", ").withColumn(\n", "    \"Month\", month(col(\"OrderDate\"))\n", ").withColumn(\n", "    \"DayOfYear\", dayofyear(col(\"OrderDate\"))\n", ").withColumn(\n", "    \"DayOfWeek\", dayofweek(col(\"OrderDate\"))\n", ").withColumn(\n", "    \"Quarter\", quarter(col(\"OrderDate\"))\n", ").withColumn(\n", "    \"IsWeekend\", when(dayofweek(col(\"OrderDate\")).isin([1, 7]), 1).otherwise(0)\n", ").withColumn(\n", "    \"IsMonthEnd\", when(col(\"OrderDate\") == last_day(col(\"OrderDate\")), 1).otherwise(0)\n", ")\n", "\n", "# Add moving averages and lag features\n", "window_7d = Window.partitionBy(\"Category\", \"Subcategory\").orderBy(\"OrderDate\").rowsBetween(-6, 0)\n", "window_30d = Window.partitionBy(\"Category\", \"Subcategory\").orderBy(\"OrderDate\").rowsBetween(-29, 0)\n", "window_lag = Window.partitionBy(\"Category\", \"Subcategory\").orderBy(\"OrderDate\")\n", "\n", "df_ml_ready = df_ml_features.withColumn(\n", "    \"Revenue_7DayMA\", avg(\"DailyRevenue\").over(window_7d)\n", ").withColumn(\n", "    \"Revenue_30DayMA\", avg(\"DailyRevenue\").over(window_30d)\n", ").withColumn(\n", "    \"Revenue_Lag1\", lag(\"DailyRevenue\", 1).over(window_lag)\n", ").withColumn(\n", "    \"Revenue_Lag7\", lag(\"DailyRevenue\", 7).over(window_lag)\n", ").withColumn(\n", "    # Growth rates\n", "    \"Revenue_Growth_1D\",\n", "    when(col(\"Revenue_Lag1\") > 0, \n", "         (col(\"DailyRevenue\") - col(\"Revenue_Lag1\")) / col(\"Revenue_Lag1\") * 100)\n", "    .otherwise(0)\n", ").withColumn(\n", "    \"Revenue_Growth_7D\",\n", "    when(col(\"Revenue_Lag7\") > 0,\n", "         (col(\"DailyRevenue\") - col(\"Revenue_Lag7\")) / col(\"Revenue_Lag7\") * 100)\n", "    .otherwise(0)\n", ").withColumn(\n", "    \"ProcessedTimestamp\", current_timestamp()\n", ")\n", "\n", "print(\"✅ ML-Ready Time Series Dataset created\")\n", "print(f\"Total records: {df_ml_ready.count():,}\")\n", "date_range = df_ml_ready.agg(min('OrderDate'), max('OrderDate')).collect()[0]\n", "print(f\"Date range: {date_range[0]} to {date_range[1]}\")\n", "print(f\"Categories: {df_ml_ready.select('Category').distinct().count()}\")\n", "print(f\"Subcategories: {df_ml_ready.select('Subcategory').distinct().count()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Save Gold Layer Tables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def write_to_gold(df, table_name, catalog, schema, partition_cols=None, mode=\"overwrite\"):\n", "    \"\"\"Write DataFrame to the Gold catalog as a Delta table\"\"\"\n", "    try:\n", "        full_table_name = f\"{catalog}.{schema}.{table_name}\"\n", "        \n", "        writer = (df.write\n", "                 .format(\"delta\")\n", "                 .mode(mode)\n", "                 .option(\"mergeSchema\", \"true\")\n", "                )\n", "        \n", "        if partition_cols:\n", "            writer = writer.partitionBy(*partition_cols)\n", "            print(f\"   📁 Partitioned by: {partition_cols}\")\n", "        \n", "        writer.saveAsTable(full_table_name)\n", "        \n", "        # Optimize table\n", "        spark.sql(f\"OPTIMIZE {full_table_name}\")\n", "        spark.sql(f\"ANALYZE TABLE {full_table_name} COMPUTE STATISTICS\")\n", "        \n", "        print(f\"✓ Successfully wrote {table_name} to {full_table_name}\")\n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"✗ Error writing {table_name} to catalog: {str(e)}\")\n", "        return False\n", "\n", "# Gold layer tables mapping\n", "gold_tables = [\n", "    (df_executive_kpis, \"executive_kpis\", [\"Year\", \"Month\"]),\n", "    (df_customer_segments, \"customer_rfm_segments\", None),\n", "    (df_abc_analysis, \"product_abc_analysis\", [\"ABC_Category\"]),\n", "    (df_sales_performance, \"sales_performance_scorecard\", [\"Year\", \"Month\"]),\n", "    (df_ml_ready, \"ml_time_series_features\", [\"Year\", \"Month\"])\n", "]\n", "\n", "print(f\"\\n🥇 Writing Gold layer tables to {catalog}.{gold_schema}...\")\n", "success_count = 0\n", "\n", "for df, table_name, partition_cols in gold_tables:\n", "    print(f\"\\n💾 Processing {table_name}...\")\n", "    if write_to_gold(df, table_name, catalog, gold_schema, partition_cols):\n", "        success_count += 1\n", "\n", "print(f\"\\n🎉 Gold Layer Complete!\")\n", "print(f\"✅ Successfully created {success_count}/{len(gold_tables)} Gold tables\")\n", "print(f\"\\n📊 Available for:\")\n", "print(f\"   • Business Intelligence Dashboards\")\n", "print(f\"   • Executive Reporting & KPIs\")\n", "print(f\"   • Machine Learning Models\")\n", "print(f\"   • Advanced Analytics\")\n", "print(f\"   • Data Science Analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Business Value Summary\n", "\n", "### **For Business Analysts:**\n", "✅ **Executive dashboards** with key business metrics  \n", "✅ **Customer segmentation** for marketing campaigns  \n", "✅ **Product performance** analysis for inventory decisions  \n", "✅ **Sales performance** tracking and reporting  \n", "\n", "### **For Data Scientists:**\n", "✅ **ML-ready datasets** with engineered features  \n", "✅ **Time series data** for forecasting models  \n", "✅ **Customer behavior** data for predictive analytics  \n", "✅ **Product analytics** for recommendation systems  \n", "\n", "### **For Executives:**\n", "✅ **KPI dashboards** for strategic decisions  \n", "✅ **Performance scorecards** for team management  \n", "✅ **Business insights** for operational improvements  \n", "✅ **Trend analysis** for planning and forecasting  \n", "\n", "### **Gold Layer Tables Created:**\n", "1. **`executive_kpis`** - Monthly business metrics by category\n", "2. **`customer_rfm_segments`** - Customer segmentation analysis\n", "3. **`product_abc_analysis`** - Product performance and inventory insights\n", "4. **`sales_performance_scorecard`** - Salesperson performance tracking\n", "5. **`ml_time_series_features`** - ML-ready time series data\n", "\n", "All tables are optimized with Delta Lake format and ready for business consumption! 🚀"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}