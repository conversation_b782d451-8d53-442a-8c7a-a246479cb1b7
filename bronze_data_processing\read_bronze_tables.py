# Adventure Works Bronze Layer Data Reader
# This script reads all parquet files from the bronze folder in ADLS

from pyspark.sql import SparkSession
from pyspark.sql.functions import *

# Initialize Spark session (if not already available)
# spark = SparkSession.builder.appName("AdventureWorksBronzeReader").getOrCreate()

# ADLS file path configuration
filePath = r"abfss://<EMAIL>/"

# Catalog and schema configuration
catalog = "adventure_works"
schema = "bronze_schema"

# List of bronze files with correct case-sensitive paths
bronze_files = [
    "Product/Product.parquet",
    "Region/Region.parquet",
    "Reseller/Reseller.parquet",
    "Sales/Sales.parquet",
    "Salesperson/Salesperson.parquet",
    "SalespersonRegion/SalespersonRegion.parquet",
    "Targets/Targets.parquet"
]

# Extract table names for processing (lowercase for consistency)
bronze_tables = [file_path.split('/')[0].lower() for file_path in bronze_files]

# Dictionary to store all DataFrames
dataframes = {}

print("Reading Adventure Works Bronze Layer Tables...")
print("=" * 50)

# Read all parquet files with correct paths
for i, table_name in enumerate(bronze_tables):
    file_path = bronze_files[i]
    try:
        print(f"Reading {table_name} table from {file_path}...")

        # Read parquet file
        df = (spark.read
              .format('parquet')
              .load(filePath + file_path)
        )

        # Store DataFrame in dictionary
        dataframes[f"df_{table_name}"] = df

        # Display basic info about the table
        print(f"✓ {table_name.upper()} - Rows: {df.count()}, Columns: {len(df.columns)}")
        print(f"  Columns: {', '.join(df.columns)}")

        # Show first few rows
        print(f"  Sample data:")
        df.show(5, truncate=False)
        print("-" * 50)

    except Exception as e:
        print(f"✗ Error reading {table_name} from {file_path}: {str(e)}")
        print("-" * 50)

print("All bronze tables loaded successfully!")
print("\nAvailable DataFrames:")
for df_name in dataframes.keys():
    print(f"- {df_name}")

# Optional: Create temporary views for SQL queries
print("\nCreating temporary views...")
for table_name in bronze_tables:
    df_name = f"df_{table_name}"
    if df_name in dataframes:
        dataframes[df_name].createOrReplaceTempView(f"bronze_{table_name}")
        print(f"✓ Created view: bronze_{table_name}")

print("\nYou can now use these DataFrames:")
print("Example usage:")
print("- dataframes['df_product'].show()")
print("- spark.sql('SELECT * FROM bronze_sales LIMIT 10').show()")
