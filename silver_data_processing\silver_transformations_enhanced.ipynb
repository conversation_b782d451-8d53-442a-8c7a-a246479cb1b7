# Import required libraries
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql.window import Window
import re

# Catalog and schema configuration
catalog = "adventworks"
bronze_schema = "bronze_schema"
silver_schema = "silver_schema"

spark.sql(f"USE CATALOG {catalog}")
spark.sql(f"USE SCHEMA {bronze_schema}")

# Create silver schema if it doesn't exist
spark.sql(f"CREATE SCHEMA IF NOT EXISTS {catalog}.{silver_schema}")

print(f"Working with catalog: {catalog}")
print(f"Bronze schema: {bronze_schema}")
print(f"Silver schema: {silver_schema}")

# Read bronze product data
df_product_bronze = spark.table(f"{catalog}.{bronze_schema}.product")

# Enhanced Product transformations
df_product_silver = df_product_bronze.select(
    col("ProductKey"),
    col("Product").alias("ProductName"),
    col("Standard_Cost").alias("StandardCost"),
    col("Color"),
    col("Subcategory"),
    col("Category"),
    
    # 🔍 BUSINESS INTELLIGENCE TRANSFORMATIONS
    
    # Extract size from product name (for bikes, clothing, etc.)
    regexp_extract(col("Product"), r"(\d+)", 1).alias("ExtractedSize"),
    
    # Extract product line/series (HL, ML, LL)
    regexp_extract(col("Product"), r"^(HL|ML|LL)", 1).alias("ProductLine"),
    
    # Product tier based on naming convention
    when(col("Product").startswith("HL"), "High-End")
    .when(col("Product").startswith("ML"), "Mid-Range")
    .when(col("Product").startswith("LL"), "Entry-Level")
    .when(col("Product").contains("100"), "Premium")
    .when(col("Product").contains("200"), "Standard")
    .when(col("Product").contains("300"), "Basic")
    .otherwise("Unclassified").alias("ProductTier"),
    
    # Cost categories for pricing strategy
    when(col("Standard_Cost") >= 1000, "High-Cost")
    .when(col("Standard_Cost") >= 100, "Medium-Cost")
    .when(col("Standard_Cost") >= 10, "Low-Cost")
    .otherwise("Ultra-Low-Cost").alias("CostCategory"),
    
    # Gender targeting (for clothing/bikes)
    when(col("Product").contains("-W ") | col("Product").contains("Women"), "Women")
    .when(col("Product").contains("Men"), "Men")
    .otherwise("Unisex").alias("TargetGender"),
    
    # Seasonal products
    when(col("Subcategory").isin(["Gloves", "Tights", "Vests"]), "Winter")
    .when(col("Subcategory").isin(["Shorts", "Jerseys"]), "Summer")
    .otherwise("All-Season").alias("Seasonality"),
    
    # Product complexity (number of variants)
    when(col("Product").contains("Frame") | col("Product").contains("Bike"), "Complex")
    .when(col("Category") == "Components", "Moderate")
    .otherwise("Simple").alias("ProductComplexity"),
    
    # Color standardization and grouping
    when(col("Color").isin(["Black", "White", "Silver", "Grey"]), "Neutral")
    .when(col("Color").isin(["Red", "Blue", "Yellow"]), "Primary")
    .when(col("Color") == "Multi", "Multi-Color")
    .when(col("Color") == "NA", "No Color")
    .otherwise("Other").alias("ColorGroup"),
    
    # Existing color formatting (keeping your good work!)
    col("Background_Color_Format"),
    col("Font_Color_Format"),
    
    # Data quality flags
    when(col("Standard_Cost").isNull() | (col("Standard_Cost") <= 0), "Invalid Cost")
    .when(col("Color").isNull(), "Missing Color")
    .otherwise("Valid").alias("DataQualityFlag"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp"),
    lit("silver_transformation_v1").alias("TransformationVersion")
)

print("✅ Product Silver transformations completed")
df_product_silver.show(5, truncate=False)

# Read bronze sales data
df_sales_bronze = spark.table(f"{catalog}.{bronze_schema}.sales")

# Enhanced Sales transformations
df_sales_silver = df_sales_bronze.select(
    col("SalesOrderNumber"),
    col("SalesOrderLineNumber"),
    col("OrderDate"),
    col("ProductKey"),
    col("CustomerKey"),
    col("SalespersonKey"),
    col("OrderQuantity"),
    col("UnitPrice"),
    col("SalesAmount"),
    col("TaxAmount"),
    col("Freight"),
    
    # 💰 REVENUE & PROFITABILITY ANALYSIS
    
    # Total order value including tax and freight
    (col("SalesAmount") + col("TaxAmount") + col("Freight")).alias("TotalOrderValue"),
    
    # Revenue per unit
    (col("SalesAmount") / col("OrderQuantity")).alias("RevenuePerUnit"),
    
    # Tax rate calculation
    when(col("SalesAmount") > 0, (col("TaxAmount") / col("SalesAmount") * 100))
    .otherwise(0).alias("TaxRatePercent"),
    
    # Order size categories
    when(col("OrderQuantity") >= 10, "Bulk")
    .when(col("OrderQuantity") >= 5, "Medium")
    .when(col("OrderQuantity") >= 2, "Small")
    .otherwise("Single").alias("OrderSizeCategory"),
    
    # Revenue tiers
    when(col("SalesAmount") >= 5000, "High-Value")
    .when(col("SalesAmount") >= 1000, "Medium-Value")
    .when(col("SalesAmount") >= 100, "Low-Value")
    .otherwise("Minimal-Value").alias("RevenueCategory"),
    
    # 📅 TIME-BASED ANALYSIS
    
    # Extract date components
    year(col("OrderDate")).alias("OrderYear"),
    month(col("OrderDate")).alias("OrderMonth"),
    quarter(col("OrderDate")).alias("OrderQuarter"),
    dayofweek(col("OrderDate")).alias("OrderDayOfWeek"),
    weekofyear(col("OrderDate")).alias("OrderWeek"),
    
    # Business day classification
    when(dayofweek(col("OrderDate")).isin([1, 7]), "Weekend")
    .otherwise("Weekday").alias("DayType"),
    
    # Season classification
    when(month(col("OrderDate")).isin([12, 1, 2]), "Winter")
    .when(month(col("OrderDate")).isin([3, 4, 5]), "Spring")
    .when(month(col("OrderDate")).isin([6, 7, 8]), "Summer")
    .otherwise("Fall").alias("Season"),
    
    # Month name for reporting
    date_format(col("OrderDate"), "MMMM").alias("OrderMonthName"),
    
    # Days since epoch (for trend analysis)
    datediff(col("OrderDate"), lit("1900-01-01")).alias("DaysSinceEpoch"),
    
    # 🎯 BUSINESS METRICS
    
    # Discount calculation (if UnitPrice vs SalesAmount/Quantity differs)
    when(col("OrderQuantity") > 0,
         ((col("UnitPrice") - (col("SalesAmount") / col("OrderQuantity"))) / col("UnitPrice") * 100))
    .otherwise(0).alias("DiscountPercent"),
    
    # Sales performance flags
    when(col("SalesAmount") >= 2000, "High-Performer")
    .when(col("SalesAmount") >= 500, "Good-Performer")
    .otherwise("Standard").alias("SalesPerformanceFlag"),
    
    # Data quality checks
    when(col("SalesAmount") <= 0, "Invalid Sales Amount")
    .when(col("OrderQuantity") <= 0, "Invalid Quantity")
    .when(col("UnitPrice") <= 0, "Invalid Unit Price")
    .otherwise("Valid").alias("DataQualityFlag"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp"),
    lit("silver_transformation_v1").alias("TransformationVersion")
)

print("✅ Sales Silver transformations completed")
df_sales_silver.show(5, truncate=False)

# Read bronze region and reseller data
df_region_bronze = spark.table(f"{catalog}.{bronze_schema}.region")
df_reseller_bronze = spark.table(f"{catalog}.{bronze_schema}.reseller")

# Enhanced Region transformations
df_region_silver = df_region_bronze.select(
    col("SalesTerritoryKey"),
    col("Region"),
    col("Country"),
    col("Group").alias("RegionGroup"),
    
    # 🌍 GEOGRAPHIC INTELLIGENCE
    
    # Continent classification
    when(col("Country").isin(["United States", "Canada"]), "North America")
    .when(col("Country").isin(["United Kingdom", "France", "Germany"]), "Europe")
    .when(col("Country") == "Australia", "Oceania")
    .otherwise("Other").alias("Continent"),
    
    # Market maturity
    when(col("Country").isin(["United States", "United Kingdom", "Germany"]), "Mature")
    .when(col("Country").isin(["France", "Canada"]), "Developing")
    .otherwise("Emerging").alias("MarketMaturity"),
    
    # Language group (for marketing)
    when(col("Country").isin(["United States", "United Kingdom", "Canada", "Australia"]), "English")
    .when(col("Country") == "France", "French")
    .when(col("Country") == "Germany", "German")
    .otherwise("Other").alias("PrimaryLanguage"),
    
    # Time zone groups (for operational planning)
    when(col("Country").isin(["United States", "Canada"]), "Americas")
    .when(col("Country").isin(["United Kingdom", "France", "Germany"]), "EMEA")
    .when(col("Country") == "Australia", "APAC")
    .otherwise("Other").alias("TimeZoneGroup"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp")
)

# Enhanced Reseller transformations
df_reseller_silver = df_reseller_bronze.select(
    col("ResellerKey"),
    col("BusinessType"),
    col("Reseller").alias("ResellerName"),
    col("City"),
    col("StateProvince"),
    col("CountryRegion"),
    col("PostalCode"),
    
    # 🏪 BUSINESS INTELLIGENCE
    
    # Business size classification
    when(col("BusinessType").contains("Warehouse"), "Large")
    .when(col("BusinessType").contains("Specialty"), "Medium")
    .when(col("BusinessType").contains("Value"), "Small")
    .otherwise("Unknown").alias("BusinessSize"),
    
    # Channel type
    when(col("BusinessType").contains("Warehouse"), "Wholesale")
    .when(col("BusinessType").contains("Specialty"), "Specialty Retail")
    .otherwise("General Retail").alias("ChannelType"),
    
    # Geographic tier (based on common business locations)
    when(col("City").isin(["Seattle", "New York", "Los Angeles", "London", "Paris"]), "Tier 1")
    .when(col("StateProvince").isin(["California", "New York", "Washington"]), "Tier 2")
    .otherwise("Tier 3").alias("GeographicTier"),
    
    # Postal code analysis (for logistics)
    length(col("PostalCode")).alias("PostalCodeLength"),
    
    # Data quality
    when(col("PostalCode").isNull() | (col("PostalCode") == ""), "Missing Postal Code")
    .when(col("City").isNull(), "Missing City")
    .otherwise("Valid").alias("DataQualityFlag"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp")
)

print("✅ Region and Reseller Silver transformations completed")
print("Region sample:")
df_region_silver.show(3, truncate=False)
print("\nReseller sample:")
df_reseller_silver.show(3, truncate=False)

# Read bronze salesperson and targets data
df_salesperson_bronze = spark.table(f"{catalog}.{bronze_schema}.salesperson")
df_targets_bronze = spark.table(f"{catalog}.{bronze_schema}.targets")

# Enhanced Salesperson transformations
df_salesperson_silver = df_salesperson_bronze.select(
    col("SalespersonKey"),
    col("Salesperson").alias("SalespersonName"),
    col("Title"),
    col("UPN").alias("UserPrincipalName"),
    
    # 👤 HR & PERFORMANCE ANALYTICS
    
    # Extract first and last name
    split(col("Salesperson"), " ").getItem(0).alias("FirstName"),
    split(col("Salesperson"), " ").getItem(1).alias("LastName"),
    
    # Seniority level from title
    when(col("Title").contains("Manager"), "Management")
    .when(col("Title").contains("Senior"), "Senior")
    .when(col("Title").contains("Representative"), "Representative")
    .otherwise("Other").alias("SeniorityLevel"),
    
    # Department classification
    when(col("Title").contains("Sales"), "Sales")
    .when(col("Title").contains("Marketing"), "Marketing")
    .otherwise("Other").alias("Department"),
    
    # Email domain analysis
    regexp_extract(col("UPN"), "@(.+)", 1).alias("EmailDomain"),
    
    # Username length (for system analysis)
    length(regexp_extract(col("UPN"), "(.+)@", 1)).alias("UsernameLength"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp")
)

# Enhanced Targets transformations
df_targets_silver = df_targets_bronze.select(
    col("SalespersonKey"),
    col("TargetBase"),
    
    # 🎯 TARGET ANALYSIS
    
    # Target categories
    when(col("TargetBase") >= 1000000, "High-Target")
    .when(col("TargetBase") >= 500000, "Medium-Target")
    .when(col("TargetBase") >= 100000, "Low-Target")
    .otherwise("Minimal-Target").alias("TargetCategory"),
    
    # Monthly target (assuming annual)
    (col("TargetBase") / 12).alias("MonthlyTarget"),
    
    # Quarterly target
    (col("TargetBase") / 4).alias("QuarterlyTarget"),
    
    # Daily target (assuming 250 working days)
    (col("TargetBase") / 250).alias("DailyTarget"),
    
    # Target tier for compensation planning
    when(col("TargetBase") >= 1500000, "Tier 1")
    .when(col("TargetBase") >= 750000, "Tier 2")
    .when(col("TargetBase") >= 250000, "Tier 3")
    .otherwise("Tier 4").alias("TargetTier"),
    
    # Metadata
    current_timestamp().alias("ProcessedTimestamp")
)

print("✅ Salesperson and Targets Silver transformations completed")
print("Salesperson sample:")
df_salesperson_silver.show(3, truncate=False)
print("\nTargets sample:")
df_targets_silver.show(3, truncate=False)